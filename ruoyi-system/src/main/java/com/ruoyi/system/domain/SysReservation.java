package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 预约管理对象 sys_reservation
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public class SysReservation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 预约ID */
    private Long reservationId;

    /** 预约编码 */
    @Excel(name = "预约编码")
    private String reservationCode;

    /** 预约名称 */
    @Excel(name = "预约名称")
    private String reservationName;

    /** 场地ID */
    @Excel(name = "场地ID")
    private Long venueId;

    /** 场地名称 */
    @Excel(name = "场地名称")
    private String venueName;

    /** 体测日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "体测日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date testDate;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 男生名额 */
    @Excel(name = "男生名额")
    private Long maleQuota;

    /** 女生名额 */
    @Excel(name = "女生名额")
    private Long femaleQuota;

    /** 男生已预约 */
    @Excel(name = "男生已预约")
    private Long maleReserved;

    /** 女生已预约 */
    @Excel(name = "女生已预约")
    private Long femaleReserved;

    /** 总名额 */
    @Excel(name = "总名额")
    private Long totalQuota;

    /** 总已预约 */
    @Excel(name = "总已预约")
    private Long totalReserved;

    /** 预约类型（FITNESS_TEST体测） */
    @Excel(name = "预约类型", readConverterExp = "F=ITNESS_TEST体测")
    private String reservationType;

    /** 测试项目 */
    @Excel(name = "测试项目")
    private String testItems;

    /** 预约要求 */
    @Excel(name = "预约要求")
    private String requirements;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 是否开放（0关闭 1开放） */
    @Excel(name = "是否开放", readConverterExp = "0=关闭,1=开放")
    private String isOpen;

    /** 显示顺序 */
    private Long orderNum;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setReservationId(Long reservationId) 
    {
        this.reservationId = reservationId;
    }

    public Long getReservationId() 
    {
        return reservationId;
    }

    public void setReservationCode(String reservationCode) 
    {
        this.reservationCode = reservationCode;
    }

    public String getReservationCode() 
    {
        return reservationCode;
    }

    public void setReservationName(String reservationName) 
    {
        this.reservationName = reservationName;
    }

    public String getReservationName() 
    {
        return reservationName;
    }

    public void setVenueId(Long venueId) 
    {
        this.venueId = venueId;
    }

    public Long getVenueId() 
    {
        return venueId;
    }

    public void setVenueName(String venueName) 
    {
        this.venueName = venueName;
    }

    public String getVenueName() 
    {
        return venueName;
    }

    public void setTestDate(Date testDate) 
    {
        this.testDate = testDate;
    }

    public Date getTestDate() 
    {
        return testDate;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setMaleQuota(Long maleQuota) 
    {
        this.maleQuota = maleQuota;
    }

    public Long getMaleQuota() 
    {
        return maleQuota;
    }

    public void setFemaleQuota(Long femaleQuota) 
    {
        this.femaleQuota = femaleQuota;
    }

    public Long getFemaleQuota() 
    {
        return femaleQuota;
    }

    public void setMaleReserved(Long maleReserved) 
    {
        this.maleReserved = maleReserved;
    }

    public Long getMaleReserved() 
    {
        return maleReserved;
    }

    public void setFemaleReserved(Long femaleReserved) 
    {
        this.femaleReserved = femaleReserved;
    }

    public Long getFemaleReserved() 
    {
        return femaleReserved;
    }

    public void setTotalQuota(Long totalQuota) 
    {
        this.totalQuota = totalQuota;
    }

    public Long getTotalQuota() 
    {
        return totalQuota;
    }

    public void setTotalReserved(Long totalReserved) 
    {
        this.totalReserved = totalReserved;
    }

    public Long getTotalReserved() 
    {
        return totalReserved;
    }

    public void setReservationType(String reservationType) 
    {
        this.reservationType = reservationType;
    }

    public String getReservationType() 
    {
        return reservationType;
    }

    public void setTestItems(String testItems) 
    {
        this.testItems = testItems;
    }

    public String getTestItems() 
    {
        return testItems;
    }

    public void setRequirements(String requirements) 
    {
        this.requirements = requirements;
    }

    public String getRequirements() 
    {
        return requirements;
    }

    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setIsOpen(String isOpen) 
    {
        this.isOpen = isOpen;
    }

    public String getIsOpen() 
    {
        return isOpen;
    }

    public void setOrderNum(Long orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Long getOrderNum() 
    {
        return orderNum;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("reservationId", getReservationId())
            .append("reservationCode", getReservationCode())
            .append("reservationName", getReservationName())
            .append("venueId", getVenueId())
            .append("venueName", getVenueName())
            .append("testDate", getTestDate())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("maleQuota", getMaleQuota())
            .append("femaleQuota", getFemaleQuota())
            .append("maleReserved", getMaleReserved())
            .append("femaleReserved", getFemaleReserved())
            .append("totalQuota", getTotalQuota())
            .append("totalReserved", getTotalReserved())
            .append("reservationType", getReservationType())
            .append("testItems", getTestItems())
            .append("requirements", getRequirements())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("isOpen", getIsOpen())
            .append("orderNum", getOrderNum())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
