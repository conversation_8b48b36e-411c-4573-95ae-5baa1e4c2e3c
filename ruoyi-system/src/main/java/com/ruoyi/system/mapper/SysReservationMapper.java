package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysReservation;

/**
 * 预约管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface SysReservationMapper 
{
    /**
     * 查询预约管理
     * 
     * @param reservationId 预约管理主键
     * @return 预约管理
     */
    public SysReservation selectSysReservationByReservationId(Long reservationId);

    /**
     * 查询预约管理列表
     * 
     * @param sysReservation 预约管理
     * @return 预约管理集合
     */
    public List<SysReservation> selectSysReservationList(SysReservation sysReservation);

    /**
     * 新增预约管理
     * 
     * @param sysReservation 预约管理
     * @return 结果
     */
    public int insertSysReservation(SysReservation sysReservation);

    /**
     * 修改预约管理
     * 
     * @param sysReservation 预约管理
     * @return 结果
     */
    public int updateSysReservation(SysReservation sysReservation);

    /**
     * 删除预约管理
     * 
     * @param reservationId 预约管理主键
     * @return 结果
     */
    public int deleteSysReservationByReservationId(Long reservationId);

    /**
     * 批量删除预约管理
     * 
     * @param reservationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysReservationByReservationIds(String[] reservationIds);
}
