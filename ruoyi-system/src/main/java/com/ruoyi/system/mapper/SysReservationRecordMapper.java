package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysReservationRecord;

/**
 * 预约记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface SysReservationRecordMapper 
{
    /**
     * 查询预约记录
     * 
     * @param recordId 预约记录主键
     * @return 预约记录
     */
    public SysReservationRecord selectSysReservationRecordByRecordId(Long recordId);

    /**
     * 查询预约记录列表
     * 
     * @param sysReservationRecord 预约记录
     * @return 预约记录集合
     */
    public List<SysReservationRecord> selectSysReservationRecordList(SysReservationRecord sysReservationRecord);

    /**
     * 新增预约记录
     * 
     * @param sysReservationRecord 预约记录
     * @return 结果
     */
    public int insertSysReservationRecord(SysReservationRecord sysReservationRecord);

    /**
     * 修改预约记录
     * 
     * @param sysReservationRecord 预约记录
     * @return 结果
     */
    public int updateSysReservationRecord(SysReservationRecord sysReservationRecord);

    /**
     * 删除预约记录
     * 
     * @param recordId 预约记录主键
     * @return 结果
     */
    public int deleteSysReservationRecordByRecordId(Long recordId);

    /**
     * 批量删除预约记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysReservationRecordByRecordIds(String[] recordIds);
}
