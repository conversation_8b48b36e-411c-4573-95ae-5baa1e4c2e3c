package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SysReservationRecord;

/**
 * 预约记录 数据层
 * 
 * <AUTHOR>
 */
public interface SysReservationRecordMapper
{
    /**
     * 查询预约记录列表
     * 
     * @param record 预约记录信息
     * @return 预约记录集合
     */
    public List<SysReservationRecord> selectRecordList(SysReservationRecord record);

    /**
     * 通过记录ID查询预约记录信息
     * 
     * @param recordId 记录ID
     * @return 预约记录信息
     */
    public SysReservationRecord selectRecordById(Long recordId);

    /**
     * 通过预约ID查询预约记录列表
     * 
     * @param reservationId 预约ID
     * @return 预约记录集合
     */
    public List<SysReservationRecord> selectRecordByReservationId(Long reservationId);

    /**
     * 通过用户ID查询预约记录列表
     * 
     * @param userId 用户ID
     * @return 预约记录集合
     */
    public List<SysReservationRecord> selectRecordByUserId(Long userId);

    /**
     * 新增预约记录
     * 
     * @param record 预约记录信息
     * @return 结果
     */
    public int insertRecord(SysReservationRecord record);

    /**
     * 修改预约记录
     * 
     * @param record 预约记录信息
     * @return 结果
     */
    public int updateRecord(SysReservationRecord record);

    /**
     * 批量删除预约记录
     * 
     * @param recordIds 需要删除的记录ID
     * @return 结果
     */
    public int deleteRecordByIds(Long[] recordIds);

    /**
     * 删除预约记录信息
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    public int deleteRecordById(Long recordId);

    /**
     * 检查用户是否已预约
     * 
     * @param reservationId 预约ID
     * @param userId 用户ID
     * @return 预约记录
     */
    public SysReservationRecord checkUserReservation(Long reservationId, Long userId);

    /**
     * 取消预约
     * 
     * @param record 预约记录信息
     * @return 结果
     */
    public int cancelReservation(SysReservationRecord record);

    /**
     * 签到
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    public int checkIn(Long recordId);

    /**
     * 统计预约数量
     * 
     * @param reservationId 预约ID
     * @param gender 性别
     * @return 数量
     */
    public int countReservationByGender(Long reservationId, String gender);

    /**
     * 根据预约ID删除预约记录
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    public int deleteRecordByReservationId(Long reservationId);
}
