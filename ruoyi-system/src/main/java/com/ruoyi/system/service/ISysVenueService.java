package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysVenue;

/**
 * 场地管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
public interface ISysVenueService 
{
    /**
     * 查询场地管理
     * 
     * @param venueId 场地管理主键
     * @return 场地管理
     */
    public SysVenue selectSysVenueByVenueId(Long venueId);

    /**
     * 查询场地管理列表
     * 
     * @param sysVenue 场地管理
     * @return 场地管理集合
     */
    public List<SysVenue> selectSysVenueList(SysVenue sysVenue);

    /**
     * 新增场地管理
     * 
     * @param sysVenue 场地管理
     * @return 结果
     */
    public int insertSysVenue(SysVenue sysVenue);

    /**
     * 修改场地管理
     * 
     * @param sysVenue 场地管理
     * @return 结果
     */
    public int updateSysVenue(SysVenue sysVenue);

    /**
     * 批量删除场地管理
     * 
     * @param venueIds 需要删除的场地管理主键集合
     * @return 结果
     */
    public int deleteSysVenueByVenueIds(String venueIds);

    /**
     * 删除场地管理信息
     * 
     * @param venueId 场地管理主键
     * @return 结果
     */
    public int deleteSysVenueByVenueId(Long venueId);
}
