package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SysReservation;

/**
 * 预约管理 服务层
 * 
 * <AUTHOR>
 */
public interface ISysReservationService
{
    /**
     * 查询预约管理列表
     * 
     * @param reservation 预约管理信息
     * @return 预约管理集合
     */
    public List<SysReservation> selectReservationList(SysReservation reservation);

    /**
     * 查询所有预约管理
     * 
     * @return 预约管理集合
     */
    public List<SysReservation> selectReservationAll();

    /**
     * 通过预约ID查询预约管理信息
     * 
     * @param reservationId 预约ID
     * @return 预约管理信息
     */
    public SysReservation selectReservationById(Long reservationId);

    /**
     * 通过预约编码查询预约管理信息
     * 
     * @param reservationCode 预约编码
     * @return 预约管理信息
     */
    public SysReservation selectReservationByCode(String reservationCode);

    /**
     * 新增预约管理
     * 
     * @param reservation 预约管理信息
     * @return 结果
     */
    public int insertReservation(SysReservation reservation);

    /**
     * 修改预约管理
     * 
     * @param reservation 预约管理信息
     * @return 结果
     */
    public int updateReservation(SysReservation reservation);

    /**
     * 批量删除预约管理
     * 
     * @param reservationIds 需要删除的预约ID
     * @return 结果
     */
    public int deleteReservationByIds(String reservationIds);

    /**
     * 删除预约管理信息
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    public int deleteReservationById(Long reservationId);

    /**
     * 校验预约编码是否唯一
     * 
     * @param reservation 预约信息
     * @return 结果
     */
    public boolean checkReservationCodeUnique(SysReservation reservation);

    /**
     * 开放预约
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    public int openReservation(Long reservationId);

    /**
     * 关闭预约
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    public int closeReservation(Long reservationId);

    /**
     * 克隆预约
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    public int cloneReservation(Long reservationId);

    /**
     * 更新预约统计数据
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    public int updateReservationStats(Long reservationId);

    /**
     * 根据场地ID查询预约列表
     * 
     * @param venueId 场地ID
     * @return 预约集合
     */
    public List<SysReservation> selectReservationByVenueId(Long venueId);

    /**
     * 查询开放的预约列表
     * 
     * @param reservation 预约信息
     * @return 预约集合
     */
    public List<SysReservation> selectOpenReservationList(SysReservation reservation);

    /**
     * 导入预约数据
     * 
     * @param reservationList 预约数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importReservation(List<SysReservation> reservationList, Boolean isUpdateSupport, String operName);
}
