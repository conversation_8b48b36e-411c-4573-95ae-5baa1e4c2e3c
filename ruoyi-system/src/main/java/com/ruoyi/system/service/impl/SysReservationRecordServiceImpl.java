package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.core.domain.entity.SysReservation;
import com.ruoyi.common.core.domain.entity.SysReservationRecord;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysReservationMapper;
import com.ruoyi.system.mapper.SysReservationRecordMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysReservationRecordService;
import com.ruoyi.system.service.ISysReservationService;

/**
 * 预约记录 服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SysReservationRecordServiceImpl implements ISysReservationRecordService
{
    @Autowired
    private SysReservationRecordMapper recordMapper;

    @Autowired
    private SysReservationMapper reservationMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private ISysReservationService reservationService;

    /**
     * 查询预约记录列表
     * 
     * @param record 预约记录信息
     * @return 预约记录集合
     */
    @Override
    public List<SysReservationRecord> selectRecordList(SysReservationRecord record)
    {
        return recordMapper.selectRecordList(record);
    }

    /**
     * 通过记录ID查询预约记录信息
     * 
     * @param recordId 记录ID
     * @return 预约记录信息
     */
    @Override
    public SysReservationRecord selectRecordById(Long recordId)
    {
        return recordMapper.selectRecordById(recordId);
    }

    /**
     * 通过预约ID查询预约记录列表
     * 
     * @param reservationId 预约ID
     * @return 预约记录集合
     */
    @Override
    public List<SysReservationRecord> selectRecordByReservationId(Long reservationId)
    {
        return recordMapper.selectRecordByReservationId(reservationId);
    }

    /**
     * 通过用户ID查询预约记录列表
     * 
     * @param userId 用户ID
     * @return 预约记录集合
     */
    @Override
    public List<SysReservationRecord> selectRecordByUserId(Long userId)
    {
        return recordMapper.selectRecordByUserId(userId);
    }

    /**
     * 新增预约记录
     * 
     * @param record 预约记录信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRecord(SysReservationRecord record)
    {
        record.setCreateTime(DateUtils.getNowDate());
        record.setDelFlag("0");
        if (StringUtils.isEmpty(record.getStatus()))
        {
            record.setStatus("0");
        }
        if (StringUtils.isEmpty(record.getRecordStatus()))
        {
            record.setRecordStatus("0");
        }
        if (StringUtils.isEmpty(record.getIsCheckedIn()))
        {
            record.setIsCheckedIn("0");
        }
        
        int result = recordMapper.insertRecord(record);
        
        // 更新预约统计数据
        if (result > 0)
        {
            reservationService.updateReservationStats(record.getReservationId());
        }
        
        return result;
    }

    /**
     * 修改预约记录
     * 
     * @param record 预约记录信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRecord(SysReservationRecord record)
    {
        record.setUpdateTime(DateUtils.getNowDate());
        
        int result = recordMapper.updateRecord(record);
        
        // 更新预约统计数据
        if (result > 0)
        {
            reservationService.updateReservationStats(record.getReservationId());
        }
        
        return result;
    }

    /**
     * 批量删除预约记录
     * 
     * @param recordIds 需要删除的记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRecordByIds(String recordIds)
    {
        Long[] ids = Convert.toLongArray(recordIds);
        
        // 获取预约ID用于更新统计
        Long reservationId = null;
        if (ids.length > 0)
        {
            SysReservationRecord record = recordMapper.selectRecordById(ids[0]);
            if (record != null)
            {
                reservationId = record.getReservationId();
            }
        }
        
        int result = recordMapper.deleteRecordByIds(ids);
        
        // 更新预约统计数据
        if (result > 0 && reservationId != null)
        {
            reservationService.updateReservationStats(reservationId);
        }
        
        return result;
    }

    /**
     * 删除预约记录信息
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRecordById(Long recordId)
    {
        SysReservationRecord record = recordMapper.selectRecordById(recordId);
        if (record == null)
        {
            return 0;
        }
        
        int result = recordMapper.deleteRecordById(recordId);
        
        // 更新预约统计数据
        if (result > 0)
        {
            reservationService.updateReservationStats(record.getReservationId());
        }
        
        return result;
    }

    /**
     * 检查用户是否已预约
     * 
     * @param reservationId 预约ID
     * @param userId 用户ID
     * @return 预约记录
     */
    @Override
    public SysReservationRecord checkUserReservation(Long reservationId, Long userId)
    {
        return recordMapper.checkUserReservation(reservationId, userId);
    }

    /**
     * 用户预约
     * 
     * @param reservationId 预约ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int makeReservation(Long reservationId, Long userId)
    {
        // 检查预约是否存在且开放
        SysReservation reservation = reservationMapper.selectReservationById(reservationId);
        if (reservation == null)
        {
            throw new ServiceException("预约不存在");
        }
        if (!"1".equals(reservation.getIsOpen()))
        {
            throw new ServiceException("预约已关闭");
        }
        if (!"0".equals(reservation.getStatus()))
        {
            throw new ServiceException("预约已停用");
        }
        
        // 检查用户是否已预约
        SysReservationRecord existRecord = recordMapper.checkUserReservation(reservationId, userId);
        if (existRecord != null)
        {
            throw new ServiceException("您已预约过此场次");
        }
        
        // 获取用户信息
        SysUser user = userMapper.selectUserById(userId);
        if (user == null)
        {
            throw new ServiceException("用户不存在");
        }
        
        // 检查名额是否充足
        String gender = user.getSex();
        if ("0".equals(gender)) // 男生
        {
            if (reservation.getMaleReserved() >= reservation.getMaleQuota())
            {
                throw new ServiceException("男生名额已满");
            }
        }
        else if ("1".equals(gender)) // 女生
        {
            if (reservation.getFemaleReserved() >= reservation.getFemaleQuota())
            {
                throw new ServiceException("女生名额已满");
            }
        }
        
        // 创建预约记录
        SysReservationRecord record = new SysReservationRecord();
        record.setReservationId(reservationId);
        record.setUserId(userId);
        record.setUserName(user.getUserName());
        record.setStudentId(user.getUserName()); // 假设用户名就是学号
        record.setGender(user.getSex());
        record.setPhone(user.getPhonenumber());
        record.setEmail(user.getEmail());
        record.setDeptName(user.getDept() != null ? user.getDept().getDeptName() : "");
        record.setReservationTime(DateUtils.getNowDate());
        record.setIsCheckedIn("0");
        record.setRecordStatus("0");
        record.setStatus("0");
        record.setDelFlag("0");
        record.setCreateBy(user.getUserName());
        record.setCreateTime(DateUtils.getNowDate());
        
        int result = recordMapper.insertRecord(record);
        
        // 更新预约统计数据
        if (result > 0)
        {
            reservationService.updateReservationStats(reservationId);
        }
        
        return result;
    }

    /**
     * 取消预约
     * 
     * @param recordId 记录ID
     * @param cancelReason 取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelReservation(Long recordId, String cancelReason)
    {
        SysReservationRecord record = recordMapper.selectRecordById(recordId);
        if (record == null)
        {
            throw new ServiceException("预约记录不存在");
        }
        
        if (!"0".equals(record.getRecordStatus()))
        {
            throw new ServiceException("预约已取消，无法重复操作");
        }
        
        record.setRecordStatus("1");
        record.setCancelReason(cancelReason);
        record.setCancelTime(DateUtils.getNowDate());
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateTime(DateUtils.getNowDate());
        
        int result = recordMapper.cancelReservation(record);
        
        // 更新预约统计数据
        if (result > 0)
        {
            reservationService.updateReservationStats(record.getReservationId());
        }
        
        return result;
    }

    /**
     * 签到
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    @Override
    public int checkIn(Long recordId)
    {
        SysReservationRecord record = recordMapper.selectRecordById(recordId);
        if (record == null)
        {
            throw new ServiceException("预约记录不存在");
        }
        
        if (!"0".equals(record.getRecordStatus()))
        {
            throw new ServiceException("预约已取消，无法签到");
        }
        
        if ("1".equals(record.getIsCheckedIn()))
        {
            throw new ServiceException("已签到，无法重复操作");
        }
        
        return recordMapper.checkIn(recordId);
    }

    /**
     * 根据预约ID删除预约记录
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    @Override
    public int deleteRecordByReservationId(Long reservationId)
    {
        return recordMapper.deleteRecordByReservationId(reservationId);
    }
}
