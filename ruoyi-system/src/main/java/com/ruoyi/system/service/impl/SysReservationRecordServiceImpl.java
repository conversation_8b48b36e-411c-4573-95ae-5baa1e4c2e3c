package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysReservationRecordMapper;
import com.ruoyi.system.domain.SysReservationRecord;
import com.ruoyi.system.service.ISysReservationRecordService;
import com.ruoyi.common.core.text.Convert;

/**
 * 预约记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class SysReservationRecordServiceImpl implements ISysReservationRecordService 
{
    @Autowired
    private SysReservationRecordMapper sysReservationRecordMapper;

    /**
     * 查询预约记录
     * 
     * @param recordId 预约记录主键
     * @return 预约记录
     */
    @Override
    public SysReservationRecord selectSysReservationRecordByRecordId(Long recordId)
    {
        return sysReservationRecordMapper.selectSysReservationRecordByRecordId(recordId);
    }

    /**
     * 查询预约记录列表
     * 
     * @param sysReservationRecord 预约记录
     * @return 预约记录
     */
    @Override
    public List<SysReservationRecord> selectSysReservationRecordList(SysReservationRecord sysReservationRecord)
    {
        return sysReservationRecordMapper.selectSysReservationRecordList(sysReservationRecord);
    }

    /**
     * 新增预约记录
     * 
     * @param sysReservationRecord 预约记录
     * @return 结果
     */
    @Override
    public int insertSysReservationRecord(SysReservationRecord sysReservationRecord)
    {
        sysReservationRecord.setCreateTime(DateUtils.getNowDate());
        return sysReservationRecordMapper.insertSysReservationRecord(sysReservationRecord);
    }

    /**
     * 修改预约记录
     * 
     * @param sysReservationRecord 预约记录
     * @return 结果
     */
    @Override
    public int updateSysReservationRecord(SysReservationRecord sysReservationRecord)
    {
        sysReservationRecord.setUpdateTime(DateUtils.getNowDate());
        return sysReservationRecordMapper.updateSysReservationRecord(sysReservationRecord);
    }

    /**
     * 批量删除预约记录
     * 
     * @param recordIds 需要删除的预约记录主键
     * @return 结果
     */
    @Override
    public int deleteSysReservationRecordByRecordIds(String recordIds)
    {
        return sysReservationRecordMapper.deleteSysReservationRecordByRecordIds(Convert.toStrArray(recordIds));
    }

    /**
     * 删除预约记录信息
     * 
     * @param recordId 预约记录主键
     * @return 结果
     */
    @Override
    public int deleteSysReservationRecordByRecordId(Long recordId)
    {
        return sysReservationRecordMapper.deleteSysReservationRecordByRecordId(recordId);
    }
}
