package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysVenueMapper;
import com.ruoyi.system.domain.SysVenue;
import com.ruoyi.system.service.ISysVenueService;
import com.ruoyi.common.core.text.Convert;

/**
 * 场地管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Service
public class SysVenueServiceImpl implements ISysVenueService 
{
    @Autowired
    private SysVenueMapper sysVenueMapper;

    /**
     * 查询场地管理
     * 
     * @param venueId 场地管理主键
     * @return 场地管理
     */
    @Override
    public SysVenue selectSysVenueByVenueId(Long venueId)
    {
        return sysVenueMapper.selectSysVenueByVenueId(venueId);
    }

    /**
     * 查询场地管理列表
     * 
     * @param sysVenue 场地管理
     * @return 场地管理
     */
    @Override
    public List<SysVenue> selectSysVenueList(SysVenue sysVenue)
    {
        return sysVenueMapper.selectSysVenueList(sysVenue);
    }

    /**
     * 新增场地管理
     * 
     * @param sysVenue 场地管理
     * @return 结果
     */
    @Override
    public int insertSysVenue(SysVenue sysVenue)
    {
        sysVenue.setCreateTime(DateUtils.getNowDate());
        return sysVenueMapper.insertSysVenue(sysVenue);
    }

    /**
     * 修改场地管理
     * 
     * @param sysVenue 场地管理
     * @return 结果
     */
    @Override
    public int updateSysVenue(SysVenue sysVenue)
    {
        sysVenue.setUpdateTime(DateUtils.getNowDate());
        return sysVenueMapper.updateSysVenue(sysVenue);
    }

    /**
     * 批量删除场地管理
     * 
     * @param venueIds 需要删除的场地管理主键
     * @return 结果
     */
    @Override
    public int deleteSysVenueByVenueIds(String venueIds)
    {
        return sysVenueMapper.deleteSysVenueByVenueIds(Convert.toStrArray(venueIds));
    }

    /**
     * 删除场地管理信息
     * 
     * @param venueId 场地管理主键
     * @return 结果
     */
    @Override
    public int deleteSysVenueByVenueId(Long venueId)
    {
        return sysVenueMapper.deleteSysVenueByVenueId(venueId);
    }
}
