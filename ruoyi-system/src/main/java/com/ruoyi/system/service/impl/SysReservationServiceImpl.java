package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.entity.SysReservation;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import javax.validation.Validator;
import com.ruoyi.system.mapper.SysReservationMapper;
import com.ruoyi.system.mapper.SysReservationRecordMapper;
import com.ruoyi.system.service.ISysReservationService;

/**
 * 预约管理 服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SysReservationServiceImpl implements ISysReservationService
{
    @Autowired
    private SysReservationMapper reservationMapper;

    @Autowired
    private SysReservationRecordMapper recordMapper;

    /**
     * 查询预约管理列表
     * 
     * @param reservation 预约管理信息
     * @return 预约管理集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysReservation> selectReservationList(SysReservation reservation)
    {
        return reservationMapper.selectReservationList(reservation);
    }

    /**
     * 查询所有预约管理
     * 
     * @return 预约管理集合
     */
    @Override
    public List<SysReservation> selectReservationAll()
    {
        return reservationMapper.selectReservationAll();
    }

    /**
     * 通过预约ID查询预约管理信息
     * 
     * @param reservationId 预约ID
     * @return 预约管理信息
     */
    @Override
    public SysReservation selectReservationById(Long reservationId)
    {
        return reservationMapper.selectReservationById(reservationId);
    }

    /**
     * 通过预约编码查询预约管理信息
     * 
     * @param reservationCode 预约编码
     * @return 预约管理信息
     */
    @Override
    public SysReservation selectReservationByCode(String reservationCode)
    {
        return reservationMapper.selectReservationByCode(reservationCode);
    }

    /**
     * 新增预约管理
     * 
     * @param reservation 预约管理信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertReservation(SysReservation reservation)
    {
        // 校验预约编码唯一性
        if (!checkReservationCodeUnique(reservation))
        {
            throw new ServiceException("新增预约失败，预约编码已存在");
        }
        
        // 设置默认值
        reservation.setCreateTime(DateUtils.getNowDate());
        reservation.setDelFlag("0");
        if (StringUtils.isEmpty(reservation.getStatus()))
        {
            reservation.setStatus("0");
        }
        if (StringUtils.isEmpty(reservation.getIsOpen()))
        {
            reservation.setIsOpen("1");
        }
        if (reservation.getMaleQuota() == null)
        {
            reservation.setMaleQuota(0);
        }
        if (reservation.getFemaleQuota() == null)
        {
            reservation.setFemaleQuota(0);
        }
        if (reservation.getTotalQuota() == null)
        {
            reservation.setTotalQuota(reservation.getMaleQuota() + reservation.getFemaleQuota());
        }
        reservation.setMaleReserved(0);
        reservation.setFemaleReserved(0);
        reservation.setTotalReserved(0);
        
        return reservationMapper.insertReservation(reservation);
    }

    /**
     * 修改预约管理
     * 
     * @param reservation 预约管理信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateReservation(SysReservation reservation)
    {
        // 校验预约编码唯一性
        if (!checkReservationCodeUnique(reservation))
        {
            throw new ServiceException("修改预约失败，预约编码已存在");
        }
        
        reservation.setUpdateTime(DateUtils.getNowDate());
        
        // 重新计算总名额
        if (reservation.getMaleQuota() != null && reservation.getFemaleQuota() != null)
        {
            reservation.setTotalQuota(reservation.getMaleQuota() + reservation.getFemaleQuota());
        }
        
        return reservationMapper.updateReservation(reservation);
    }

    /**
     * 批量删除预约管理
     * 
     * @param reservationIds 需要删除的预约ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteReservationByIds(String reservationIds)
    {
        Long[] ids = Convert.toLongArray(reservationIds);
        
        // 删除预约记录
        for (Long reservationId : ids)
        {
            recordMapper.deleteRecordByReservationId(reservationId);
        }
        
        return reservationMapper.deleteReservationByIds(ids);
    }

    /**
     * 删除预约管理信息
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteReservationById(Long reservationId)
    {
        // 删除预约记录
        recordMapper.deleteRecordByReservationId(reservationId);
        
        return reservationMapper.deleteReservationById(reservationId);
    }

    /**
     * 校验预约编码是否唯一
     * 
     * @param reservation 预约信息
     * @return 结果
     */
    @Override
    public boolean checkReservationCodeUnique(SysReservation reservation)
    {
        Long reservationId = StringUtils.isNull(reservation.getReservationId()) ? -1L : reservation.getReservationId();
        SysReservation info = reservationMapper.checkReservationCodeUnique(reservation.getReservationCode());
        if (StringUtils.isNotNull(info) && info.getReservationId().longValue() != reservationId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 开放预约
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    @Override
    public int openReservation(Long reservationId)
    {
        return reservationMapper.openReservation(reservationId);
    }

    /**
     * 关闭预约
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    @Override
    public int closeReservation(Long reservationId)
    {
        return reservationMapper.closeReservation(reservationId);
    }

    /**
     * 克隆预约
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    @Override
    @Transactional
    public int cloneReservation(Long reservationId)
    {
        SysReservation original = reservationMapper.selectReservationById(reservationId);
        if (original == null)
        {
            throw new ServiceException("克隆失败，原预约不存在");
        }
        
        // 创建克隆对象
        SysReservation clone = new SysReservation();
        clone.setReservationCode(original.getReservationCode() + "_CLONE_" + System.currentTimeMillis());
        clone.setReservationName(original.getReservationName() + "（克隆）");
        clone.setVenueId(original.getVenueId());
        clone.setVenueName(original.getVenueName());
        clone.setTestDate(original.getTestDate());
        clone.setStartTime(original.getStartTime());
        clone.setEndTime(original.getEndTime());
        clone.setMaleQuota(original.getMaleQuota());
        clone.setFemaleQuota(original.getFemaleQuota());
        clone.setTotalQuota(original.getTotalQuota());
        clone.setReservationType(original.getReservationType());
        clone.setTestItems(original.getTestItems());
        clone.setRequirements(original.getRequirements());
        clone.setContactPerson(original.getContactPerson());
        clone.setContactPhone(original.getContactPhone());
        clone.setIsOpen("0"); // 克隆的预约默认关闭
        clone.setOrderNum(original.getOrderNum());
        clone.setStatus("0");
        clone.setDelFlag("0");
        clone.setCreateTime(DateUtils.getNowDate());
        clone.setMaleReserved(0);
        clone.setFemaleReserved(0);
        clone.setTotalReserved(0);
        clone.setRemark("克隆自：" + original.getReservationName());
        
        return reservationMapper.insertReservation(clone);
    }

    /**
     * 更新预约统计数据
     * 
     * @param reservationId 预约ID
     * @return 结果
     */
    @Override
    public int updateReservationStats(Long reservationId)
    {
        // 统计男生预约数量
        int maleCount = recordMapper.countReservationByGender(reservationId, "0");
        // 统计女生预约数量
        int femaleCount = recordMapper.countReservationByGender(reservationId, "1");
        
        SysReservation reservation = new SysReservation();
        reservation.setReservationId(reservationId);
        reservation.setMaleReserved(maleCount);
        reservation.setFemaleReserved(femaleCount);
        reservation.setTotalReserved(maleCount + femaleCount);
        
        return reservationMapper.updateReservationStats(reservation);
    }

    /**
     * 根据场地ID查询预约列表
     * 
     * @param venueId 场地ID
     * @return 预约集合
     */
    @Override
    public List<SysReservation> selectReservationByVenueId(Long venueId)
    {
        return reservationMapper.selectReservationByVenueId(venueId);
    }

    /**
     * 查询开放的预约列表
     * 
     * @param reservation 预约信息
     * @return 预约集合
     */
    @Override
    public List<SysReservation> selectOpenReservationList(SysReservation reservation)
    {
        return reservationMapper.selectOpenReservationList(reservation);
    }

    /**
     * 导入预约数据
     * 
     * @param reservationList 预约数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importReservation(List<SysReservation> reservationList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(reservationList) || reservationList.size() == 0)
        {
            throw new ServiceException("导入预约数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (SysReservation reservation : reservationList)
        {
            try
            {
                // 验证是否存在这个预约
                SysReservation r = reservationMapper.selectReservationByCode(reservation.getReservationCode());
                if (StringUtils.isNull(r))
                {
                    BeanValidators.validateWithException(reservation);
                    reservation.setCreateBy(operName);
                    this.insertReservation(reservation);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、预约 " + reservation.getReservationName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    BeanValidators.validateWithException(reservation);
                    reservation.setReservationId(r.getReservationId());
                    reservation.setUpdateBy(operName);
                    this.updateReservation(reservation);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、预约 " + reservation.getReservationName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、预约 " + reservation.getReservationName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、预约 " + reservation.getReservationName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
