<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysReservationRecordMapper">
    
    <resultMap type="SysReservationRecord" id="SysReservationRecordResult">
        <result property="recordId"         column="record_id"         />
        <result property="reservationId"    column="reservation_id"    />
        <result property="userId"           column="user_id"           />
        <result property="userName"         column="user_name"         />
        <result property="studentId"        column="student_id"        />
        <result property="gender"           column="gender"            />
        <result property="phone"            column="phone"             />
        <result property="email"            column="email"             />
        <result property="deptName"         column="dept_name"         />
        <result property="className"        column="class_name"        />
        <result property="reservationTime"  column="reservation_time"  />
        <result property="checkInTime"      column="check_in_time"     />
        <result property="isCheckedIn"      column="is_checked_in"     />
        <result property="testResult"       column="test_result"       />
        <result property="recordStatus"     column="record_status"     />
        <result property="cancelReason"     column="cancel_reason"     />
        <result property="cancelTime"       column="cancel_time"       />
        <result property="status"           column="status"            />
        <result property="delFlag"          column="del_flag"          />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
        <result property="remark"           column="remark"            />
    </resultMap>

    <sql id="selectRecordVo">
        select record_id, reservation_id, user_id, user_name, student_id, gender, phone, email, dept_name, class_name,
               reservation_time, check_in_time, is_checked_in, test_result, record_status, cancel_reason, cancel_time,
               status, del_flag, create_by, create_time, update_by, update_time, remark 
        from sys_reservation_record
    </sql>

    <select id="selectRecordList" parameterType="SysReservationRecord" resultMap="SysReservationRecordResult">
        <include refid="selectRecordVo"/>
        <where>  
            <if test="reservationId != null"> and reservation_id = #{reservationId}</if>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="userName != null and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="studentId != null and studentId != ''"> and student_id like concat('%', #{studentId}, '%')</if>
            <if test="gender != null and gender != ''"> and gender = #{gender}</if>
            <if test="deptName != null and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="className != null and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="isCheckedIn != null and isCheckedIn != ''"> and is_checked_in = #{isCheckedIn}</if>
            <if test="recordStatus != null and recordStatus != ''"> and record_status = #{recordStatus}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by reservation_time desc
    </select>
    
    <select id="selectRecordById" parameterType="Long" resultMap="SysReservationRecordResult">
        <include refid="selectRecordVo"/>
        where record_id = #{recordId} and del_flag = '0'
    </select>
    
    <select id="selectRecordByReservationId" parameterType="Long" resultMap="SysReservationRecordResult">
        <include refid="selectRecordVo"/>
        where reservation_id = #{reservationId} and del_flag = '0' and record_status = '0'
        order by reservation_time desc
    </select>
    
    <select id="selectRecordByUserId" parameterType="Long" resultMap="SysReservationRecordResult">
        <include refid="selectRecordVo"/>
        where user_id = #{userId} and del_flag = '0'
        order by reservation_time desc
    </select>

    <select id="checkUserReservation" resultMap="SysReservationRecordResult">
        <include refid="selectRecordVo"/>
        where reservation_id = #{reservationId} and user_id = #{userId} and del_flag = '0' and record_status = '0'
        limit 1
    </select>

    <select id="countReservationByGender" resultType="int">
        select count(*) from sys_reservation_record 
        where reservation_id = #{reservationId} and gender = #{gender} and del_flag = '0' and record_status = '0'
    </select>

    <insert id="insertRecord" parameterType="SysReservationRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into sys_reservation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reservationId != null">reservation_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="studentId != null">student_id,</if>
            <if test="gender != null">gender,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="className != null">class_name,</if>
            <if test="reservationTime != null">reservation_time,</if>
            <if test="checkInTime != null">check_in_time,</if>
            <if test="isCheckedIn != null">is_checked_in,</if>
            <if test="testResult != null">test_result,</if>
            <if test="recordStatus != null">record_status,</if>
            <if test="cancelReason != null">cancel_reason,</if>
            <if test="cancelTime != null">cancel_time,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reservationId != null">#{reservationId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="gender != null">#{gender},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="className != null">#{className},</if>
            <if test="reservationTime != null">#{reservationTime},</if>
            <if test="checkInTime != null">#{checkInTime},</if>
            <if test="isCheckedIn != null">#{isCheckedIn},</if>
            <if test="testResult != null">#{testResult},</if>
            <if test="recordStatus != null">#{recordStatus},</if>
            <if test="cancelReason != null">#{cancelReason},</if>
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRecord" parameterType="SysReservationRecord">
        update sys_reservation_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="reservationId != null">reservation_id = #{reservationId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="reservationTime != null">reservation_time = #{reservationTime},</if>
            <if test="checkInTime != null">check_in_time = #{checkInTime},</if>
            <if test="isCheckedIn != null">is_checked_in = #{isCheckedIn},</if>
            <if test="testResult != null">test_result = #{testResult},</if>
            <if test="recordStatus != null">record_status = #{recordStatus},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteRecordById" parameterType="Long">
        update sys_reservation_record set del_flag = '2' where record_id = #{recordId}
    </delete>

    <delete id="deleteRecordByIds" parameterType="String">
        update sys_reservation_record set del_flag = '2' where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <delete id="deleteRecordByReservationId" parameterType="Long">
        update sys_reservation_record set del_flag = '2' where reservation_id = #{reservationId}
    </delete>

    <update id="cancelReservation" parameterType="SysReservationRecord">
        update sys_reservation_record 
        set record_status = '1', 
            cancel_reason = #{cancelReason}, 
            cancel_time = #{cancelTime},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where record_id = #{recordId}
    </update>

    <update id="checkIn" parameterType="Long">
        update sys_reservation_record 
        set is_checked_in = '1', 
            check_in_time = now()
        where record_id = #{recordId}
    </update>

</mapper>
