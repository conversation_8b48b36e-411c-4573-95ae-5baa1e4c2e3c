<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysReservationRecordMapper">
    
    <resultMap type="SysReservationRecord" id="SysReservationRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="reservationId"    column="reservation_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="studentId"    column="student_id"    />
        <result property="gender"    column="gender"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="deptName"    column="dept_name"    />
        <result property="className"    column="class_name"    />
        <result property="reservationTime"    column="reservation_time"    />
        <result property="checkInTime"    column="check_in_time"    />
        <result property="isCheckedIn"    column="is_checked_in"    />
        <result property="testResult"    column="test_result"    />
        <result property="recordStatus"    column="record_status"    />
        <result property="cancelReason"    column="cancel_reason"    />
        <result property="cancelTime"    column="cancel_time"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysReservationRecordVo">
        select record_id, reservation_id, user_id, user_name, student_id, gender, phone, email, dept_name, class_name, reservation_time, check_in_time, is_checked_in, test_result, record_status, cancel_reason, cancel_time, status, del_flag, create_by, create_time, update_by, update_time, remark from sys_reservation_record
    </sql>

    <select id="selectSysReservationRecordList" parameterType="SysReservationRecord" resultMap="SysReservationRecordResult">
        <include refid="selectSysReservationRecordVo"/>
        <where>  
            <if test="reservationId != null "> and reservation_id = #{reservationId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="reservationTime != null "> and reservation_time = #{reservationTime}</if>
            <if test="checkInTime != null "> and check_in_time = #{checkInTime}</if>
            <if test="isCheckedIn != null  and isCheckedIn != ''"> and is_checked_in = #{isCheckedIn}</if>
            <if test="testResult != null  and testResult != ''"> and test_result = #{testResult}</if>
            <if test="recordStatus != null  and recordStatus != ''"> and record_status = #{recordStatus}</if>
            <if test="cancelReason != null  and cancelReason != ''"> and cancel_reason = #{cancelReason}</if>
            <if test="cancelTime != null "> and cancel_time = #{cancelTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysReservationRecordByRecordId" parameterType="Long" resultMap="SysReservationRecordResult">
        <include refid="selectSysReservationRecordVo"/>
        where record_id = #{recordId}
    </select>

    <insert id="insertSysReservationRecord" parameterType="SysReservationRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into sys_reservation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reservationId != null">reservation_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="studentId != null">student_id,</if>
            <if test="gender != null">gender,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="className != null">class_name,</if>
            <if test="reservationTime != null">reservation_time,</if>
            <if test="checkInTime != null">check_in_time,</if>
            <if test="isCheckedIn != null">is_checked_in,</if>
            <if test="testResult != null">test_result,</if>
            <if test="recordStatus != null">record_status,</if>
            <if test="cancelReason != null">cancel_reason,</if>
            <if test="cancelTime != null">cancel_time,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reservationId != null">#{reservationId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="gender != null">#{gender},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="className != null">#{className},</if>
            <if test="reservationTime != null">#{reservationTime},</if>
            <if test="checkInTime != null">#{checkInTime},</if>
            <if test="isCheckedIn != null">#{isCheckedIn},</if>
            <if test="testResult != null">#{testResult},</if>
            <if test="recordStatus != null">#{recordStatus},</if>
            <if test="cancelReason != null">#{cancelReason},</if>
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysReservationRecord" parameterType="SysReservationRecord">
        update sys_reservation_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="reservationId != null">reservation_id = #{reservationId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="reservationTime != null">reservation_time = #{reservationTime},</if>
            <if test="checkInTime != null">check_in_time = #{checkInTime},</if>
            <if test="isCheckedIn != null">is_checked_in = #{isCheckedIn},</if>
            <if test="testResult != null">test_result = #{testResult},</if>
            <if test="recordStatus != null">record_status = #{recordStatus},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteSysReservationRecordByRecordId" parameterType="Long">
        delete from sys_reservation_record where record_id = #{recordId}
    </delete>

    <delete id="deleteSysReservationRecordByRecordIds" parameterType="String">
        delete from sys_reservation_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

</mapper>