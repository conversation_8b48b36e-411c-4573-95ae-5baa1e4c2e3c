<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysVenueMapper">
    
    <resultMap type="SysVenue" id="SysVenueResult">
        <result property="venueId"    column="venue_id"    />
        <result property="venueCode"    column="venue_code"    />
        <result property="venueName"    column="venue_name"    />
        <result property="venueType"    column="venue_type"    />
        <result property="capacity"    column="capacity"    />
        <result property="area"    column="area"    />
        <result property="location"    column="location"    />
        <result property="floor"    column="floor"    />
        <result property="equipment"    column="equipment"    />
        <result property="priceHour"    column="price_hour"    />
        <result property="priceDay"    column="price_day"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="description"    column="description"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysVenueVo">
        select venue_id, venue_code, venue_name, venue_type, capacity, area, location, floor, equipment, price_hour, price_day, contact_person, contact_phone, description, image_url, order_num, status, del_flag, create_by, create_time, update_by, update_time, remark from sys_venue
    </sql>

    <select id="selectSysVenueList" parameterType="SysVenue" resultMap="SysVenueResult">
        <include refid="selectSysVenueVo"/>
        <where>  
            <if test="venueCode != null  and venueCode != ''"> and venue_code = #{venueCode}</if>
            <if test="venueName != null  and venueName != ''"> and venue_name like concat('%', #{venueName}, '%')</if>
            <if test="venueType != null  and venueType != ''"> and venue_type = #{venueType}</if>
            <if test="capacity != null "> and capacity = #{capacity}</if>
            <if test="area != null "> and area = #{area}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="floor != null  and floor != ''"> and floor = #{floor}</if>
            <if test="equipment != null  and equipment != ''"> and equipment = #{equipment}</if>
            <if test="priceHour != null "> and price_hour = #{priceHour}</if>
            <if test="priceDay != null "> and price_day = #{priceDay}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysVenueByVenueId" parameterType="Long" resultMap="SysVenueResult">
        <include refid="selectSysVenueVo"/>
        where venue_id = #{venueId}
    </select>

    <insert id="insertSysVenue" parameterType="SysVenue" useGeneratedKeys="true" keyProperty="venueId">
        insert into sys_venue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="venueCode != null and venueCode != ''">venue_code,</if>
            <if test="venueName != null and venueName != ''">venue_name,</if>
            <if test="venueType != null">venue_type,</if>
            <if test="capacity != null">capacity,</if>
            <if test="area != null">area,</if>
            <if test="location != null">location,</if>
            <if test="floor != null">floor,</if>
            <if test="equipment != null">equipment,</if>
            <if test="priceHour != null">price_hour,</if>
            <if test="priceDay != null">price_day,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="description != null">description,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="venueCode != null and venueCode != ''">#{venueCode},</if>
            <if test="venueName != null and venueName != ''">#{venueName},</if>
            <if test="venueType != null">#{venueType},</if>
            <if test="capacity != null">#{capacity},</if>
            <if test="area != null">#{area},</if>
            <if test="location != null">#{location},</if>
            <if test="floor != null">#{floor},</if>
            <if test="equipment != null">#{equipment},</if>
            <if test="priceHour != null">#{priceHour},</if>
            <if test="priceDay != null">#{priceDay},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="description != null">#{description},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysVenue" parameterType="SysVenue">
        update sys_venue
        <trim prefix="SET" suffixOverrides=",">
            <if test="venueCode != null and venueCode != ''">venue_code = #{venueCode},</if>
            <if test="venueName != null and venueName != ''">venue_name = #{venueName},</if>
            <if test="venueType != null">venue_type = #{venueType},</if>
            <if test="capacity != null">capacity = #{capacity},</if>
            <if test="area != null">area = #{area},</if>
            <if test="location != null">location = #{location},</if>
            <if test="floor != null">floor = #{floor},</if>
            <if test="equipment != null">equipment = #{equipment},</if>
            <if test="priceHour != null">price_hour = #{priceHour},</if>
            <if test="priceDay != null">price_day = #{priceDay},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="description != null">description = #{description},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where venue_id = #{venueId}
    </update>

    <delete id="deleteSysVenueByVenueId" parameterType="Long">
        delete from sys_venue where venue_id = #{venueId}
    </delete>

    <delete id="deleteSysVenueByVenueIds" parameterType="String">
        delete from sys_venue where venue_id in 
        <foreach item="venueId" collection="array" open="(" separator="," close=")">
            #{venueId}
        </foreach>
    </delete>

</mapper>