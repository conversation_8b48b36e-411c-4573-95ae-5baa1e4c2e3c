<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysReservationMapper">
    
    <resultMap type="SysReservation" id="SysReservationResult">
        <result property="reservationId"    column="reservation_id"    />
        <result property="reservationCode"    column="reservation_code"    />
        <result property="reservationName"    column="reservation_name"    />
        <result property="venueId"    column="venue_id"    />
        <result property="venueName"    column="venue_name"    />
        <result property="testDate"    column="test_date"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="maleQuota"    column="male_quota"    />
        <result property="femaleQuota"    column="female_quota"    />
        <result property="maleReserved"    column="male_reserved"    />
        <result property="femaleReserved"    column="female_reserved"    />
        <result property="totalQuota"    column="total_quota"    />
        <result property="totalReserved"    column="total_reserved"    />
        <result property="reservationType"    column="reservation_type"    />
        <result property="testItems"    column="test_items"    />
        <result property="requirements"    column="requirements"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="isOpen"    column="is_open"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysReservationVo">
        select reservation_id, reservation_code, reservation_name, venue_id, venue_name, test_date, start_time, end_time, male_quota, female_quota, male_reserved, female_reserved, total_quota, total_reserved, reservation_type, test_items, requirements, contact_person, contact_phone, is_open, order_num, status, del_flag, create_by, create_time, update_by, update_time, remark from sys_reservation
    </sql>

    <select id="selectSysReservationList" parameterType="SysReservation" resultMap="SysReservationResult">
        <include refid="selectSysReservationVo"/>
        <where>  
            <if test="reservationCode != null  and reservationCode != ''"> and reservation_code = #{reservationCode}</if>
            <if test="reservationName != null  and reservationName != ''"> and reservation_name like concat('%', #{reservationName}, '%')</if>
            <if test="venueId != null "> and venue_id = #{venueId}</if>
            <if test="venueName != null  and venueName != ''"> and venue_name like concat('%', #{venueName}, '%')</if>
            <if test="testDate != null "> and test_date = #{testDate}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="maleQuota != null "> and male_quota = #{maleQuota}</if>
            <if test="femaleQuota != null "> and female_quota = #{femaleQuota}</if>
            <if test="maleReserved != null "> and male_reserved = #{maleReserved}</if>
            <if test="femaleReserved != null "> and female_reserved = #{femaleReserved}</if>
            <if test="totalQuota != null "> and total_quota = #{totalQuota}</if>
            <if test="totalReserved != null "> and total_reserved = #{totalReserved}</if>
            <if test="reservationType != null  and reservationType != ''"> and reservation_type = #{reservationType}</if>
            <if test="testItems != null  and testItems != ''"> and test_items = #{testItems}</if>
            <if test="requirements != null  and requirements != ''"> and requirements = #{requirements}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="isOpen != null  and isOpen != ''"> and is_open = #{isOpen}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysReservationByReservationId" parameterType="Long" resultMap="SysReservationResult">
        <include refid="selectSysReservationVo"/>
        where reservation_id = #{reservationId}
    </select>

    <insert id="insertSysReservation" parameterType="SysReservation" useGeneratedKeys="true" keyProperty="reservationId">
        insert into sys_reservation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reservationCode != null and reservationCode != ''">reservation_code,</if>
            <if test="reservationName != null and reservationName != ''">reservation_name,</if>
            <if test="venueId != null">venue_id,</if>
            <if test="venueName != null">venue_name,</if>
            <if test="testDate != null">test_date,</if>
            start_time,
            end_time,
            <if test="maleQuota != null">male_quota,</if>
            <if test="femaleQuota != null">female_quota,</if>
            <if test="maleReserved != null">male_reserved,</if>
            <if test="femaleReserved != null">female_reserved,</if>
            <if test="totalQuota != null">total_quota,</if>
            <if test="totalReserved != null">total_reserved,</if>
            <if test="reservationType != null">reservation_type,</if>
            <if test="testItems != null">test_items,</if>
            <if test="requirements != null">requirements,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="isOpen != null">is_open,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reservationCode != null and reservationCode != ''">#{reservationCode},</if>
            <if test="reservationName != null and reservationName != ''">#{reservationName},</if>
            <if test="venueId != null">#{venueId},</if>
            <if test="venueName != null">#{venueName},</if>
            <if test="testDate != null">#{testDate},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="maleQuota != null">#{maleQuota},</if>
            <if test="femaleQuota != null">#{femaleQuota},</if>
            <if test="maleReserved != null">#{maleReserved},</if>
            <if test="femaleReserved != null">#{femaleReserved},</if>
            <if test="totalQuota != null">#{totalQuota},</if>
            <if test="totalReserved != null">#{totalReserved},</if>
            <if test="reservationType != null">#{reservationType},</if>
            <if test="testItems != null">#{testItems},</if>
            <if test="requirements != null">#{requirements},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="isOpen != null">#{isOpen},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysReservation" parameterType="SysReservation">
        update sys_reservation
        <trim prefix="SET" suffixOverrides=",">
            <if test="reservationCode != null and reservationCode != ''">reservation_code = #{reservationCode},</if>
            <if test="reservationName != null and reservationName != ''">reservation_name = #{reservationName},</if>
            <if test="venueId != null">venue_id = #{venueId},</if>
            <if test="venueName != null">venue_name = #{venueName},</if>
            <if test="testDate != null">test_date = #{testDate},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="maleQuota != null">male_quota = #{maleQuota},</if>
            <if test="femaleQuota != null">female_quota = #{femaleQuota},</if>
            <if test="maleReserved != null">male_reserved = #{maleReserved},</if>
            <if test="femaleReserved != null">female_reserved = #{femaleReserved},</if>
            <if test="totalQuota != null">total_quota = #{totalQuota},</if>
            <if test="totalReserved != null">total_reserved = #{totalReserved},</if>
            <if test="reservationType != null">reservation_type = #{reservationType},</if>
            <if test="testItems != null">test_items = #{testItems},</if>
            <if test="requirements != null">requirements = #{requirements},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="isOpen != null">is_open = #{isOpen},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where reservation_id = #{reservationId}
    </update>

    <delete id="deleteSysReservationByReservationId" parameterType="Long">
        delete from sys_reservation where reservation_id = #{reservationId}
    </delete>

    <delete id="deleteSysReservationByReservationIds" parameterType="String">
        delete from sys_reservation where reservation_id in 
        <foreach item="reservationId" collection="array" open="(" separator="," close=")">
            #{reservationId}
        </foreach>
    </delete>

</mapper>