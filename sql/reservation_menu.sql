-- ----------------------------
-- 预约管理功能菜单权限 SQL
-- 作者：ruoyi
-- 日期：2025-01-27
-- 说明：预约管理功能的完整菜单权限配置
-- ----------------------------

-- ----------------------------
-- 1、预约管理菜单
-- ----------------------------
-- 主菜单：预约管理
insert into sys_menu values('1062', '预约管理', '1', '10', '/system/reservation', 'C', '0', 'system:reservation:view', 'fa fa-calendar', 'admin', sysdate(), '', null, '预约管理菜单');

-- 功能按钮：预约管理相关操作
insert into sys_menu values('1063', '预约查询', '1062', '1',  '#',  'F', '0', 'system:reservation:list',         '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1064', '预约新增', '1062', '2',  '#',  'F', '0', 'system:reservation:add',          '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1065', '预约修改', '1062', '3',  '#',  'F', '0', 'system:reservation:edit',         '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1066', '预约删除', '1062', '4',  '#',  'F', '0', 'system:reservation:remove',       '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1067', '预约导出', '1062', '5',  '#',  'F', '0', 'system:reservation:export',       '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1068', '开放预约', '1062', '6',  '#',  'F', '0', 'system:reservation:open',         '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1069', '关闭预约', '1062', '7',  '#',  'F', '0', 'system:reservation:close',        '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1070', '克隆预约', '1062', '8',  '#',  'F', '0', 'system:reservation:clone',        '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1071', '预约记录', '1062', '9',  '#',  'F', '0', 'system:reservation:record',       '#', 'admin', sysdate(), '', null, '');

-- ----------------------------
-- 2、角色菜单权限关联
-- ----------------------------
-- 为普通角色（role_id=2）分配预约管理权限
insert into sys_role_menu values ('2', '1062');  -- 预约管理主菜单
insert into sys_role_menu values ('2', '1063');  -- 预约查询
insert into sys_role_menu values ('2', '1064');  -- 预约新增
insert into sys_role_menu values ('2', '1065');  -- 预约修改
insert into sys_role_menu values ('2', '1066');  -- 预约删除
insert into sys_role_menu values ('2', '1067');  -- 预约导出
insert into sys_role_menu values ('2', '1068');  -- 开放预约
insert into sys_role_menu values ('2', '1069');  -- 关闭预约
insert into sys_role_menu values ('2', '1070');  -- 克隆预约
insert into sys_role_menu values ('2', '1071');  -- 预约记录

-- ----------------------------
-- 3、菜单权限说明
-- ----------------------------
/*
菜单ID分配：
- 1062: 预约管理主菜单
- 1063: 预约查询权限
- 1064: 预约新增权限
- 1065: 预约修改权限
- 1066: 预约删除权限
- 1067: 预约导出权限
- 1068: 开放预约权限
- 1069: 关闭预约权限
- 1070: 克隆预约权限
- 1071: 预约记录权限

权限标识说明：
- system:reservation:view   - 查看预约管理页面
- system:reservation:list   - 查询预约列表
- system:reservation:add    - 新增预约
- system:reservation:edit   - 修改预约
- system:reservation:remove - 删除预约
- system:reservation:export - 导出预约数据
- system:reservation:open   - 开放预约
- system:reservation:close  - 关闭预约
- system:reservation:clone  - 克隆预约
- system:reservation:record - 查看预约记录

使用说明：
1. 执行此SQL文件将为系统添加完整的预约管理功能菜单
2. 默认为角色ID=2的普通角色分配所有权限
3. 管理员角色（role_id=1）默认拥有所有权限
4. 如需为其他角色分配权限，请在sys_role_menu表中添加对应记录
*/
