package com.ruoyi.common.core.domain.entity;

import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 场地管理对象 sys_venue
 * 
 * <AUTHOR>
 */
public class SysVenue extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 场地ID */
    private Long venueId;

    /** 场地编码 */
    @Excel(name = "场地编码")
    @NotBlank(message = "场地编码不能为空")
    @Size(min = 0, max = 50, message = "场地编码长度不能超过50个字符")
    private String venueCode;

    /** 场地名称 */
    @Excel(name = "场地名称")
    @NotBlank(message = "场地名称不能为空")
    @Size(min = 0, max = 100, message = "场地名称长度不能超过100个字符")
    private String venueName;

    /** 场地类型 */
    @Excel(name = "场地类型")
    private String venueType;

    /** 容纳人数 */
    @Excel(name = "容纳人数")
    private Integer capacity;

    /** 场地面积 */
    @Excel(name = "场地面积")
    private BigDecimal area;

    /** 场地位置 */
    @Excel(name = "场地位置")
    private String location;

    /** 所在楼层 */
    @Excel(name = "所在楼层")
    private String floor;

    /** 设备设施 */
    @Excel(name = "设备设施")
    private String equipment;

    /** 每小时价格 */
    @Excel(name = "每小时价格")
    private BigDecimal priceHour;

    /** 每天价格 */
    @Excel(name = "每天价格")
    private BigDecimal priceDay;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 场地描述 */
    private String description;

    /** 场地图片 */
    private String imageUrl;

    /** 显示顺序 */
    private Integer orderNum;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志 */
    private String delFlag;

    public void setVenueId(Long venueId) 
    {
        this.venueId = venueId;
    }

    public Long getVenueId() 
    {
        return venueId;
    }

    public void setVenueCode(String venueCode) 
    {
        this.venueCode = venueCode;
    }

    public String getVenueCode() 
    {
        return venueCode;
    }

    public void setVenueName(String venueName) 
    {
        this.venueName = venueName;
    }

    public String getVenueName() 
    {
        return venueName;
    }

    public void setVenueType(String venueType) 
    {
        this.venueType = venueType;
    }

    public String getVenueType() 
    {
        return venueType;
    }

    public void setCapacity(Integer capacity) 
    {
        this.capacity = capacity;
    }

    public Integer getCapacity() 
    {
        return capacity;
    }

    public void setArea(BigDecimal area) 
    {
        this.area = area;
    }

    public BigDecimal getArea() 
    {
        return area;
    }

    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }

    public void setFloor(String floor) 
    {
        this.floor = floor;
    }

    public String getFloor() 
    {
        return floor;
    }

    public void setEquipment(String equipment) 
    {
        this.equipment = equipment;
    }

    public String getEquipment() 
    {
        return equipment;
    }

    public void setPriceHour(BigDecimal priceHour) 
    {
        this.priceHour = priceHour;
    }

    public BigDecimal getPriceHour() 
    {
        return priceHour;
    }

    public void setPriceDay(BigDecimal priceDay) 
    {
        this.priceDay = priceDay;
    }

    public BigDecimal getPriceDay() 
    {
        return priceDay;
    }

    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setImageUrl(String imageUrl) 
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl() 
    {
        return imageUrl;
    }

    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("venueId", getVenueId())
            .append("venueCode", getVenueCode())
            .append("venueName", getVenueName())
            .append("venueType", getVenueType())
            .append("capacity", getCapacity())
            .append("area", getArea())
            .append("location", getLocation())
            .append("floor", getFloor())
            .append("equipment", getEquipment())
            .append("priceHour", getPriceHour())
            .append("priceDay", getPriceDay())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("description", getDescription())
            .append("imageUrl", getImageUrl())
            .append("orderNum", getOrderNum())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
