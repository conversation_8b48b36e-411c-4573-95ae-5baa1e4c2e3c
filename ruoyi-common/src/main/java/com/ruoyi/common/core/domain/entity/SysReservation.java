package com.ruoyi.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 预约管理对象 sys_reservation
 * 
 * <AUTHOR>
 */
public class SysReservation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 预约ID */
    private Long reservationId;

    /** 预约编码 */
    @Excel(name = "预约编码")
    @NotBlank(message = "预约编码不能为空")
    @Size(min = 0, max = 50, message = "预约编码长度不能超过50个字符")
    private String reservationCode;

    /** 预约名称 */
    @Excel(name = "预约名称")
    @NotBlank(message = "预约名称不能为空")
    @Size(min = 0, max = 100, message = "预约名称长度不能超过100个字符")
    private String reservationName;

    /** 场地ID */
    @NotNull(message = "场地ID不能为空")
    private Long venueId;

    /** 场地名称 */
    @Excel(name = "场地名称")
    private String venueName;

    /** 体测日期 */
    @Excel(name = "体测日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "体测日期不能为空")
    private Date testDate;

    /** 开始时间 */
    @Excel(name = "开始时间", width = 30, dateFormat = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /** 结束时间 */
    @Excel(name = "结束时间", width = 30, dateFormat = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    /** 男生名额 */
    @Excel(name = "男生名额")
    private Integer maleQuota;

    /** 女生名额 */
    @Excel(name = "女生名额")
    private Integer femaleQuota;

    /** 男生已预约 */
    @Excel(name = "男生已预约")
    private Integer maleReserved;

    /** 女生已预约 */
    @Excel(name = "女生已预约")
    private Integer femaleReserved;

    /** 总名额 */
    @Excel(name = "总名额")
    private Integer totalQuota;

    /** 总已预约 */
    @Excel(name = "总已预约")
    private Integer totalReserved;

    /** 预约类型 */
    @Excel(name = "预约类型")
    private String reservationType;

    /** 测试项目 */
    @Excel(name = "测试项目")
    private String testItems;

    /** 预约要求 */
    private String requirements;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 是否开放 */
    @Excel(name = "是否开放", readConverterExp = "0=关闭,1=开放")
    private String isOpen;

    /** 显示顺序 */
    private Integer orderNum;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志 */
    private String delFlag;

    public void setReservationId(Long reservationId) 
    {
        this.reservationId = reservationId;
    }

    public Long getReservationId() 
    {
        return reservationId;
    }

    public void setReservationCode(String reservationCode) 
    {
        this.reservationCode = reservationCode;
    }

    public String getReservationCode() 
    {
        return reservationCode;
    }

    public void setReservationName(String reservationName) 
    {
        this.reservationName = reservationName;
    }

    public String getReservationName() 
    {
        return reservationName;
    }

    public void setVenueId(Long venueId) 
    {
        this.venueId = venueId;
    }

    public Long getVenueId() 
    {
        return venueId;
    }

    public void setVenueName(String venueName) 
    {
        this.venueName = venueName;
    }

    public String getVenueName() 
    {
        return venueName;
    }

    public void setTestDate(Date testDate) 
    {
        this.testDate = testDate;
    }

    public Date getTestDate() 
    {
        return testDate;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setMaleQuota(Integer maleQuota) 
    {
        this.maleQuota = maleQuota;
    }

    public Integer getMaleQuota() 
    {
        return maleQuota;
    }

    public void setFemaleQuota(Integer femaleQuota) 
    {
        this.femaleQuota = femaleQuota;
    }

    public Integer getFemaleQuota() 
    {
        return femaleQuota;
    }

    public void setMaleReserved(Integer maleReserved) 
    {
        this.maleReserved = maleReserved;
    }

    public Integer getMaleReserved() 
    {
        return maleReserved;
    }

    public void setFemaleReserved(Integer femaleReserved) 
    {
        this.femaleReserved = femaleReserved;
    }

    public Integer getFemaleReserved() 
    {
        return femaleReserved;
    }

    public void setTotalQuota(Integer totalQuota) 
    {
        this.totalQuota = totalQuota;
    }

    public Integer getTotalQuota() 
    {
        return totalQuota;
    }

    public void setTotalReserved(Integer totalReserved) 
    {
        this.totalReserved = totalReserved;
    }

    public Integer getTotalReserved() 
    {
        return totalReserved;
    }

    public void setReservationType(String reservationType) 
    {
        this.reservationType = reservationType;
    }

    public String getReservationType() 
    {
        return reservationType;
    }

    public void setTestItems(String testItems) 
    {
        this.testItems = testItems;
    }

    public String getTestItems() 
    {
        return testItems;
    }

    public void setRequirements(String requirements) 
    {
        this.requirements = requirements;
    }

    public String getRequirements() 
    {
        return requirements;
    }

    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setIsOpen(String isOpen) 
    {
        this.isOpen = isOpen;
    }

    public String getIsOpen() 
    {
        return isOpen;
    }

    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("reservationId", getReservationId())
            .append("reservationCode", getReservationCode())
            .append("reservationName", getReservationName())
            .append("venueId", getVenueId())
            .append("venueName", getVenueName())
            .append("testDate", getTestDate())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("maleQuota", getMaleQuota())
            .append("femaleQuota", getFemaleQuota())
            .append("maleReserved", getMaleReserved())
            .append("femaleReserved", getFemaleReserved())
            .append("totalQuota", getTotalQuota())
            .append("totalReserved", getTotalReserved())
            .append("reservationType", getReservationType())
            .append("testItems", getTestItems())
            .append("requirements", getRequirements())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("isOpen", getIsOpen())
            .append("orderNum", getOrderNum())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
