<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('预约管理列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>预约编码：</label>
                                <input type="text" name="reservationCode"/>
                            </li>
                            <li>
                                <label>预约名称：</label>
                                <input type="text" name="reservationName"/>
                            </li>
                            <li>
                                <label>场地ID：</label>
                                <input type="text" name="venueId"/>
                            </li>
                            <li>
                                <label>场地名称：</label>
                                <input type="text" name="venueName"/>
                            </li>
                            <li>
                                <label>体测日期：</label>
                                <input type="text" class="time-input" placeholder="请选择体测日期" name="testDate"/>
                            </li>
                            <li>
                                <label>开始时间：</label>
                                <input type="text" class="time-input" placeholder="请选择开始时间" name="startTime"/>
                            </li>
                            <li>
                                <label>结束时间：</label>
                                <input type="text" class="time-input" placeholder="请选择结束时间" name="endTime"/>
                            </li>
                            <li>
                                <label>男生名额：</label>
                                <input type="text" name="maleQuota"/>
                            </li>
                            <li>
                                <label>女生名额：</label>
                                <input type="text" name="femaleQuota"/>
                            </li>
                            <li>
                                <label>男生已预约：</label>
                                <input type="text" name="maleReserved"/>
                            </li>
                            <li>
                                <label>女生已预约：</label>
                                <input type="text" name="femaleReserved"/>
                            </li>
                            <li>
                                <label>总名额：</label>
                                <input type="text" name="totalQuota"/>
                            </li>
                            <li>
                                <label>总已预约：</label>
                                <input type="text" name="totalReserved"/>
                            </li>
                            <li>
                                <label>联系人：</label>
                                <input type="text" name="contactPerson"/>
                            </li>
                            <li>
                                <label>联系电话：</label>
                                <input type="text" name="contactPhone"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:reservation:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:reservation:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:reservation:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:reservation:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:reservation:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:reservation:remove')}]];
        var prefix = ctx + "system/reservation";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "预约管理",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'reservationId',
                    title: '预约ID',
                    visible: false
                },
                {
                    field: 'reservationCode',
                    title: '预约编码'
                },
                {
                    field: 'reservationName',
                    title: '预约名称'
                },
                {
                    field: 'venueId',
                    title: '场地ID'
                },
                {
                    field: 'venueName',
                    title: '场地名称'
                },
                {
                    field: 'testDate',
                    title: '体测日期'
                },
                {
                    field: 'startTime',
                    title: '开始时间'
                },
                {
                    field: 'endTime',
                    title: '结束时间'
                },
                {
                    field: 'maleQuota',
                    title: '男生名额'
                },
                {
                    field: 'femaleQuota',
                    title: '女生名额'
                },
                {
                    field: 'maleReserved',
                    title: '男生已预约'
                },
                {
                    field: 'femaleReserved',
                    title: '女生已预约'
                },
                {
                    field: 'totalQuota',
                    title: '总名额'
                },
                {
                    field: 'totalReserved',
                    title: '总已预约'
                },
                {
                    field: 'reservationType',
                    title: '预约类型'
                },
                {
                    field: 'testItems',
                    title: '测试项目'
                },
                {
                    field: 'requirements',
                    title: '预约要求'
                },
                {
                    field: 'contactPerson',
                    title: '联系人'
                },
                {
                    field: 'contactPhone',
                    title: '联系电话'
                },
                {
                    field: 'isOpen',
                    title: '是否开放'
                },
                {
                    field: 'status',
                    title: '状态'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.reservationId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.reservationId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>