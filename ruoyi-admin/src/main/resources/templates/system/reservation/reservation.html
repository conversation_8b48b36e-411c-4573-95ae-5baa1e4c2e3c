<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('预约管理列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>预约编码：</label>
                                <input type="text" name="reservationCode"/>
                            </li>
                            <li>
                                <label>预约名称：</label>
                                <input type="text" name="reservationName"/>
                            </li>
                            <li>
                                <label>场地名称：</label>
                                <input type="text" name="venueName"/>
                            </li>
                            <li>
                                <label>体测日期：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="请选择体测日期" name="testDate"/>
                            </li>
                            <li>
                                <label>预约类型：</label>
                                <select name="reservationType" th:with="type=${@dict.getType('reservation_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>开放状态：</label>
                                <select name="isOpen">
                                    <option value="">所有</option>
                                    <option value="0">关闭</option>
                                    <option value="1">开放</option>
                                </select>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:reservation:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:reservation:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:reservation:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="system:reservation:add">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:reservation:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:reservation:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:reservation:remove')}]];
        var openFlag = [[${@permission.hasPermi('system:reservation:open')}]];
        var closeFlag = [[${@permission.hasPermi('system:reservation:close')}]];
        var cloneFlag = [[${@permission.hasPermi('system:reservation:clone')}]];
        var recordFlag = [[${@permission.hasPermi('system:reservation:record')}]];
        var prefix = ctx + "system/reservation";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "预约管理",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'reservationId',
                    title: '预约ID',
                    visible: false
                },
                {
                    field: 'reservationCode',
                    title: '预约编码'
                },
                {
                    field: 'reservationName',
                    title: '预约名称'
                },
                {
                    field: 'venueName',
                    title: '场地名称'
                },
                {
                    field: 'testDate',
                    title: '体测日期'
                },
                {
                    field: 'startTime',
                    title: '开始时间'
                },
                {
                    field: 'endTime',
                    title: '结束时间'
                },
                {
                    field: 'totalQuota',
                    title: '总名额',
                    formatter: function(value, row, index) {
                        return '男:' + row.maleQuota + ' 女:' + row.femaleQuota + ' 总:' + value;
                    }
                },
                {
                    field: 'totalReserved',
                    title: '已预约',
                    formatter: function(value, row, index) {
                        return '男:' + row.maleReserved + ' 女:' + row.femaleReserved + ' 总:' + value;
                    }
                },
                {
                    field: 'reservationType',
                    title: '预约类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(reservationTypeDatas, value);
                    }
                },
                {
                    field: 'isOpen',
                    title: '开放状态',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '<span class="badge badge-success">开放</span>';
                        } else {
                            return '<span class="badge badge-danger">关闭</span>';
                        }
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.reservationId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.reservationId + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        
                        if (row.isOpen == '1') {
                            actions.push('<a class="btn btn-warning btn-xs ' + closeFlag + '" href="javascript:void(0)" onclick="closeReservation(\'' + row.reservationId + '\')"><i class="fa fa-lock"></i>关闭</a> ');
                        } else {
                            actions.push('<a class="btn btn-info btn-xs ' + openFlag + '" href="javascript:void(0)" onclick="openReservation(\'' + row.reservationId + '\')"><i class="fa fa-unlock"></i>开放</a> ');
                        }
                        
                        actions.push('<a class="btn btn-primary btn-xs ' + cloneFlag + '" href="javascript:void(0)" onclick="cloneReservation(\'' + row.reservationId + '\')"><i class="fa fa-copy"></i>克隆</a> ');
                        actions.push('<a class="btn btn-info btn-xs ' + recordFlag + '" href="javascript:void(0)" onclick="showRecord(\'' + row.reservationId + '\')"><i class="fa fa-list"></i>记录</a>');
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        var reservationTypeDatas = [[${@dict.getType('reservation_type')}]];
        var statusDatas = [[${@dict.getType('sys_normal_disable')}]];

        /* 开放预约 */
        function openReservation(reservationId) {
            $.modal.confirm("确认要开放该预约吗？", function() {
                $.operate.post(prefix + "/open", { "reservationId": reservationId });
            })
        }

        /* 关闭预约 */
        function closeReservation(reservationId) {
            $.modal.confirm("确认要关闭该预约吗？", function() {
                $.operate.post(prefix + "/close", { "reservationId": reservationId });
            })
        }

        /* 克隆预约 */
        function cloneReservation(reservationId) {
            $.modal.confirm("确认要克隆该预约吗？", function() {
                $.operate.post(prefix + "/clone", { "reservationId": reservationId });
            })
        }

        /* 查看预约记录 */
        function showRecord(reservationId) {
            var url = prefix + "/record/" + reservationId;
            $.modal.openTab("预约记录", url);
        }
    </script>
</body>
</html>
