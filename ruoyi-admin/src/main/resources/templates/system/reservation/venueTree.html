<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择场地')" />
    <th:block th:include="include :: ztree-css" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <div class="row">
            <div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-content">
                        <div id="venueTree" class="ztree"></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-9 animated fadeInRight">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form class="form-horizontal m" id="form-venue-select">
                                    <h4 class="form-header h4">场地信息</h4>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-4 control-label">场地编码：</label>
                                                <div class="col-sm-8">
                                                    <input id="venueCode" name="venueCode" class="form-control" type="text" readonly>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-4 control-label">场地名称：</label>
                                                <div class="col-sm-8">
                                                    <input id="venueName" name="venueName" class="form-control" type="text" readonly>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-4 control-label">场地类型：</label>
                                                <div class="col-sm-8">
                                                    <input id="venueType" name="venueType" class="form-control" type="text" readonly>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-4 control-label">容纳人数：</label>
                                                <div class="col-sm-8">
                                                    <input id="capacity" name="capacity" class="form-control" type="text" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-4 control-label">场地位置：</label>
                                                <div class="col-sm-8">
                                                    <input id="location" name="location" class="form-control" type="text" readonly>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-4 control-label">所在楼层：</label>
                                                <div class="col-sm-8">
                                                    <input id="floor" name="floor" class="form-control" type="text" readonly>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-4 control-label">联系人：</label>
                                                <div class="col-sm-8">
                                                    <input id="contactPerson" name="contactPerson" class="form-control" type="text" readonly>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-4 control-label">联系电话：</label>
                                                <div class="col-sm-8">
                                                    <input id="contactPhone" name="contactPhone" class="form-control" type="text" readonly>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">设备设施：</label>
                                        <div class="col-sm-10">
                                            <textarea id="equipment" name="equipment" class="form-control" readonly></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">场地描述：</label>
                                        <div class="col-sm-10">
                                            <textarea id="description" name="description" class="form-control" readonly></textarea>
                                        </div>
                                    </div>
                                    <input id="venueId" name="venueId" type="hidden">
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">
        var setting = {
            data: {
                simpleData: {
                    enable: true,
                    idKey: "venueId",
                    pIdKey: "parentId",
                    rootPId: 0
                },
                key: {
                    url: "nourl"
                }
            },
            callback: {
                onClick: onClick
            }
        };

        function onClick(e, treeId, treeNode) {
            $("#venueId").val(treeNode.venueId);
            $("#venueCode").val(treeNode.venueCode);
            $("#venueName").val(treeNode.venueName);
            $("#venueType").val(treeNode.venueType);
            $("#capacity").val(treeNode.capacity);
            $("#location").val(treeNode.location);
            $("#floor").val(treeNode.floor);
            $("#contactPerson").val(treeNode.contactPerson);
            $("#contactPhone").val(treeNode.contactPhone);
            $("#equipment").val(treeNode.equipment);
            $("#description").val(treeNode.description);
        }

        $(function() {
            // 直接调用场地管理的查询接口
            var url = ctx + "system/venue/list";
            $.post(url, { status: "0" }, function(result) {
                if (result.code == 0) {
                    var data = result.rows;
                    var treeData = [];
                    for (var i = 0; i < data.length; i++) {
                        var venue = data[i];
                        treeData.push({
                            id: venue.venueId,
                            venueId: venue.venueId,
                            name: venue.venueName + "(" + venue.venueCode + ")",
                            venueCode: venue.venueCode,
                            venueName: venue.venueName,
                            venueType: venue.venueType,
                            capacity: venue.capacity,
                            location: venue.location,
                            floor: venue.floor,
                            contactPerson: venue.contactPerson,
                            contactPhone: venue.contactPhone,
                            equipment: venue.equipment,
                            description: venue.description,
                            parentId: 0
                        });
                    }
                    $.fn.zTree.init($("#venueTree"), setting, treeData);
                } else {
                    $.modal.msgError("加载场地数据失败：" + result.msg);
                }
            });
        });
    </script>
</body>
</html>
