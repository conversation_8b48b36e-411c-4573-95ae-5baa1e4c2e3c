<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('学生预约')" />
    <th:block th:include="include :: bootstrap-table-css" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <!-- 预约信息展示 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5><i class="fa fa-calendar"></i> 体测预约信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">预约名称：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${reservation.reservationName}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">体测日期：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${#dates.format(reservation.testDate, 'yyyy-MM-dd')}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">时间段：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static">
                                            <span th:text="${#dates.format(reservation.startTime, 'HH:mm:ss')}"></span>
                                            -
                                            <span th:text="${#dates.format(reservation.endTime, 'HH:mm:ss')}"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">体测场地：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${reservation.venueName}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">预约名额：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static">
                                            <span class="text-primary">男生: <span th:text="${reservation.maleReserved ?: 0}"></span>/<span th:text="${reservation.maleQuota ?: 0}"></span></span>
                                            &nbsp;&nbsp;
                                            <span class="text-danger">女生: <span th:text="${reservation.femaleReserved ?: 0}"></span>/<span th:text="${reservation.femaleQuota ?: 0}"></span></span>
                                            &nbsp;&nbsp;
                                            <span class="text-success">总计: <span th:text="${reservation.totalReserved ?: 0}"></span>/<span th:text="${reservation.totalQuota ?: 0}"></span></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${reservation.testItems}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">测试项目：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static" th:text="${reservation.testItems}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${reservation.requirements}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">预约要求：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static" th:text="${reservation.requirements}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${reservation.contactPerson or reservation.contactPhone}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">联系方式：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static">
                                            <span th:if="${reservation.contactPerson}" th:text="${reservation.contactPerson}"></span>
                                            <span th:if="${reservation.contactPhone}"> - <span th:text="${reservation.contactPhone}"></span></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预约记录列表 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5><i class="fa fa-users"></i> 预约记录</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-sm" onclick="addReservationRecord()">
                                <i class="fa fa-plus"></i> 新增预约
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-12">
                                <table id="bootstrap-table"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增预约记录模态框 -->
    <div class="modal fade" id="reservationRecordModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">新增预约</h4>
                </div>
                <div class="modal-body">
                    <form id="reservationRecordForm" class="form-horizontal">
                        <input type="hidden" name="reservationId" th:value="${reservation.reservationId}">
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">学生姓名：</label>
                            <div class="col-sm-8">
                                <input name="studentName" class="form-control" type="text" placeholder="请输入学生姓名" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">学号：</label>
                            <div class="col-sm-8">
                                <input name="studentId" class="form-control" type="text" placeholder="请输入学号" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">性别：</label>
                            <div class="col-sm-8">
                                <select name="gender" class="form-control" required>
                                    <option value="">请选择性别</option>
                                    <option value="M">男</option>
                                    <option value="F">女</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系电话：</label>
                            <div class="col-sm-8">
                                <input name="phone" class="form-control" type="tel" placeholder="请输入联系电话">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">班级：</label>
                            <div class="col-sm-8">
                                <input name="className" class="form-control" type="text" placeholder="请输入班级">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">备注：</label>
                            <div class="col-sm-8">
                                <textarea name="remark" class="form-control" rows="3" placeholder="请输入备注信息"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitReservationRecord()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-table-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/reservation";
        var reservationId = [[${reservation.reservationId}]];

        $(function() {
            var options = {
                url: prefix + "/recordList",
                createUrl: prefix + "/record/add",
                updateUrl: prefix + "/record/edit/{id}",
                removeUrl: prefix + "/record/remove",
                exportUrl: prefix + "/record/export",
                modalName: "预约记录",
                queryParams: function(params) {
                    var search = $.table.queryParams(params);
                    search.reservationId = reservationId;
                    return search;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'recordId',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'studentName',
                    title: '学生姓名'
                },
                {
                    field: 'studentId',
                    title: '学号'
                },
                {
                    field: 'gender',
                    title: '性别',
                    formatter: function(value, row, index) {
                        if (value == 'M') {
                            return '<span class="text-primary">男</span>';
                        } else if (value == 'F') {
                            return '<span class="text-danger">女</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'phone',
                    title: '联系电话'
                },
                {
                    field: 'className',
                    title: '班级'
                },
                {
                    field: 'reservationTime',
                    title: '预约时间'
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        if (value == '0') {
                            return '<span class="badge badge-success">已预约</span>';
                        } else if (value == '1') {
                            return '<span class="badge badge-warning">已取消</span>';
                        }
                        return value;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (row.status == '0') {
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="cancelRecord(\'' + row.recordId + '\')"><i class="fa fa-times"></i>取消预约</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 新增预约记录
        function addReservationRecord() {
            $('#reservationRecordModal').modal('show');
            $('#reservationRecordForm')[0].reset();
        }

        // 提交预约记录
        function submitReservationRecord() {
            if (!$('#reservationRecordForm').valid()) {
                return;
            }
            
            var formData = $('#reservationRecordForm').serialize();
            $.ajax({
                url: prefix + "/record/add",
                type: "POST",
                data: formData,
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.msgSuccess("预约成功");
                        $('#reservationRecordModal').modal('hide');
                        $("#bootstrap-table").bootstrapTable('refresh');
                    } else {
                        $.modal.msgError(result.msg);
                    }
                }
            });
        }

        // 取消预约
        function cancelRecord(recordId) {
            $.modal.confirm("确认要取消这个预约吗？", function() {
                $.ajax({
                    url: prefix + "/record/cancel/" + recordId,
                    type: "POST",
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess("取消成功");
                            $("#bootstrap-table").bootstrapTable('refresh');
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    }
                });
            });
        }
    </script>
</body>
</html>
