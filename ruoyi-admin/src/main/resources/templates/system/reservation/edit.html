<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改预约管理')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-reservation-edit" th:object="${reservation}">
            <input name="reservationId" th:field="*{reservationId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">预约编码：</label>
                <div class="col-sm-8">
                    <input name="reservationCode" th:field="*{reservationCode}" class="form-control" type="text" placeholder="请输入预约编码" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">预约名称：</label>
                <div class="col-sm-8">
                    <input name="reservationName" th:field="*{reservationName}" class="form-control" type="text" placeholder="请输入预约名称" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">场地：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input name="venueName" th:field="*{venueName}" class="form-control" type="text" placeholder="请选择场地" readonly required>
                        <input name="venueId" th:field="*{venueId}" type="hidden">
                        <span class="input-group-addon"><i class="fa fa-search cursor-pointer" onclick="selectVenue()"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">体测日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="testDate" th:value="${#dates.format(reservation.testDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">开始时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="startTime" th:value="${#dates.format(reservation.startTime, 'HH:mm:ss')}" class="form-control" placeholder="HH:mm:ss" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">结束时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="endTime" th:value="${#dates.format(reservation.endTime, 'HH:mm:ss')}" class="form-control" placeholder="HH:mm:ss" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">男生名额：</label>
                <div class="col-sm-8">
                    <input name="maleQuota" th:field="*{maleQuota}" class="form-control" type="number" placeholder="请输入男生名额" min="0">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 已预约：<span th:text="*{maleReserved}"></span>人</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">女生名额：</label>
                <div class="col-sm-8">
                    <input name="femaleQuota" th:field="*{femaleQuota}" class="form-control" type="number" placeholder="请输入女生名额" min="0">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 已预约：<span th:text="*{femaleReserved}"></span>人</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">预约类型：</label>
                <div class="col-sm-8">
                    <select name="reservationType" class="form-control">
                        <option value="FITNESS_TEST" th:selected="*{reservationType == 'FITNESS_TEST'}">体质测试</option>
                        <option value="PHYSICAL_EXAM" th:selected="*{reservationType == 'PHYSICAL_EXAM'}">体检预约</option>
                        <option value="SPORTS_TEST" th:selected="*{reservationType == 'SPORTS_TEST'}">运动能力测试</option>
                        <option value="HEALTH_ASSESSMENT" th:selected="*{reservationType == 'HEALTH_ASSESSMENT'}">健康评估</option>
                        <option value="TRAINING_SESSION" th:selected="*{reservationType == 'TRAINING_SESSION'}">训练课程</option>
                        <option value="COMPETITION" th:selected="*{reservationType == 'COMPETITION'}">比赛活动</option>
                        <option value="MEETING" th:selected="*{reservationType == 'MEETING'}">会议预约</option>
                        <option value="OTHER" th:selected="*{reservationType == 'OTHER'}">其他</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">测试项目：</label>
                <div class="col-sm-8">
                    <textarea name="testItems" th:field="*{testItems}" class="form-control" placeholder="请输入测试项目，多个项目用逗号分隔"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">预约要求：</label>
                <div class="col-sm-8">
                    <textarea name="requirements" th:field="*{requirements}" class="form-control" placeholder="请输入预约要求"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系人：</label>
                <div class="col-sm-8">
                    <input name="contactPerson" th:field="*{contactPerson}" class="form-control" type="text" placeholder="请输入联系人">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系电话：</label>
                <div class="col-sm-8">
                    <input name="contactPhone" th:field="*{contactPhone}" class="form-control" type="text" placeholder="请输入联系电话">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否开放：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="isOpen1" name="isOpen" value="1" th:checked="*{isOpen == '1'}">
                        <label for="isOpen1">开放</label>
                        <input type="radio" id="isOpen0" name="isOpen" value="0" th:checked="*{isOpen == '0'}">
                        <label for="isOpen0">关闭</label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">显示顺序：</label>
                <div class="col-sm-8">
                    <input name="orderNum" th:field="*{orderNum}" class="form-control" type="number" placeholder="请输入显示顺序">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                        <input type="radio" th:id="${'status' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
                        <label th:for="${'status' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" th:field="*{remark}" class="form-control" placeholder="请输入备注"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/reservation";
        $("#form-reservation-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-reservation-edit').serialize());
            }
        }

        $("input[name='testDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            language: 'zh-CN',
            autoclose: true
        });

        $("input[name='startTime']").datetimepicker({
            format: "hh:ii:ss",
            startView: 1,
            maxView: 1,
            language: 'zh-CN',
            autoclose: true
        });

        $("input[name='endTime']").datetimepicker({
            format: "hh:ii:ss",
            startView: 1,
            maxView: 1,
            language: 'zh-CN',
            autoclose: true
        });

        // 选择场地
        function selectVenue() {
            var options = {
                title: '选择场地',
                width: "400",
                url: prefix + "/selectVenueTree",
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        function doSubmit(index, layero){
            var body = $.modal.getChildFrame(index);
            $("#venueId").val(body.find('#venueId').val());
            $("#venueName").val(body.find('#venueName').val());
            $.modal.close(index);
        }

        // 自动计算总名额
        $("input[name='maleQuota'], input[name='femaleQuota']").on('input', function() {
            var maleQuota = parseInt($("input[name='maleQuota']").val()) || 0;
            var femaleQuota = parseInt($("input[name='femaleQuota']").val()) || 0;
            var totalQuota = maleQuota + femaleQuota;
            $("input[name='totalQuota']").val(totalQuota);
        });

        // 名额验证
        $("input[name='maleQuota']").on('blur', function() {
            var maleQuota = parseInt($(this).val()) || 0;
            var maleReserved = parseInt([[${reservation.maleReserved}]]) || 0;
            if (maleQuota < maleReserved) {
                $.modal.alertWarning("男生名额不能小于已预约人数(" + maleReserved + ")");
                $(this).focus();
            }
        });

        $("input[name='femaleQuota']").on('blur', function() {
            var femaleQuota = parseInt($(this).val()) || 0;
            var femaleReserved = parseInt([[${reservation.femaleReserved}]]) || 0;
            if (femaleQuota < femaleReserved) {
                $.modal.alertWarning("女生名额不能小于已预约人数(" + femaleReserved + ")");
                $(this).focus();
            }
        });
    </script>
</body>
</html>
