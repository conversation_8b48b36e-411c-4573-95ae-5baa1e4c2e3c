<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('预约记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>预约信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <p><strong>预约名称：</strong><span th:text="${reservation.reservationName}"></span></p>
                                <p><strong>场地名称：</strong><span th:text="${reservation.venueName}"></span></p>
                                <p><strong>体测日期：</strong><span th:text="${#dates.format(reservation.testDate, 'yyyy-MM-dd')}"></span></p>
                                <p><strong>时间段：</strong><span th:text="${#dates.format(reservation.startTime, 'HH:mm:ss')} + ' - ' + ${#dates.format(reservation.endTime, 'HH:mm:ss')}"></span></p>
                            </div>
                            <div class="col-sm-6">
                                <p><strong>男生名额：</strong><span th:text="${reservation.maleReserved} + '/' + ${reservation.maleQuota}"></span></p>
                                <p><strong>女生名额：</strong><span th:text="${reservation.femaleReserved} + '/' + ${reservation.femaleQuota}"></span></p>
                                <p><strong>总名额：</strong><span th:text="${reservation.totalReserved} + '/' + ${reservation.totalQuota}"></span></p>
                                <p><strong>开放状态：</strong>
                                    <span th:if="${reservation.isOpen == '1'}" class="badge badge-success">开放</span>
                                    <span th:if="${reservation.isOpen == '0'}" class="badge badge-danger">关闭</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input type="hidden" name="reservationId" th:value="${reservation.reservationId}">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>用户姓名：</label>
                                <input type="text" name="userName"/>
                            </li>
                            <li>
                                <label>学号：</label>
                                <input type="text" name="studentId"/>
                            </li>
                            <li>
                                <label>性别：</label>
                                <select name="gender">
                                    <option value="">所有</option>
                                    <option value="0">男</option>
                                    <option value="1">女</option>
                                </select>
                            </li>
                            <li>
                                <label>院系：</label>
                                <input type="text" name="deptName"/>
                            </li>
                            <li>
                                <label>班级：</label>
                                <input type="text" name="className"/>
                            </li>
                            <li>
                                <label>签到状态：</label>
                                <select name="isCheckedIn">
                                    <option value="">所有</option>
                                    <option value="0">未签到</option>
                                    <option value="1">已签到</option>
                                </select>
                            </li>
                            <li>
                                <label>记录状态：</label>
                                <select name="recordStatus">
                                    <option value="">所有</option>
                                    <option value="0">正常</option>
                                    <option value="1">取消</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:reservation:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="refreshStats()">
                    <i class="fa fa-refresh"></i> 刷新统计
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:reservation:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:reservation:remove')}]];
        var reservationId = [[${reservation.reservationId}]];
        var prefix = ctx + "system/reservation";

        $(function() {
            var options = {
                url: prefix + "/record/list",
                exportUrl: prefix + "/record/export",
                modalName: "预约记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'recordId',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'userName',
                    title: '用户姓名'
                },
                {
                    field: 'studentId',
                    title: '学号'
                },
                {
                    field: 'gender',
                    title: '性别',
                    formatter: function(value, row, index) {
                        if (value == '0') {
                            return '<span class="badge badge-primary">男</span>';
                        } else if (value == '1') {
                            return '<span class="badge badge-danger">女</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'phone',
                    title: '手机号码'
                },
                {
                    field: 'deptName',
                    title: '院系'
                },
                {
                    field: 'className',
                    title: '班级'
                },
                {
                    field: 'reservationTime',
                    title: '预约时间'
                },
                {
                    field: 'isCheckedIn',
                    title: '签到状态',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '<span class="badge badge-success">已签到</span>';
                        } else {
                            return '<span class="badge badge-warning">未签到</span>';
                        }
                    }
                },
                {
                    field: 'checkInTime',
                    title: '签到时间'
                },
                {
                    field: 'recordStatus',
                    title: '记录状态',
                    formatter: function(value, row, index) {
                        if (value == '0') {
                            return '<span class="badge badge-success">正常</span>';
                        } else if (value == '1') {
                            return '<span class="badge badge-danger">取消</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'cancelReason',
                    title: '取消原因'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        
                        if (row.recordStatus == '0') {
                            if (row.isCheckedIn == '0') {
                                actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="checkIn(\'' + row.recordId + '\')"><i class="fa fa-check"></i>签到</a> ');
                            }
                            actions.push('<a class="btn btn-warning btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="cancelRecord(\'' + row.recordId + '\')"><i class="fa fa-ban"></i>取消</a> ');
                        }
                        
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showDetail(\'' + row.recordId + '\')"><i class="fa fa-eye"></i>详情</a>');
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        /* 签到 */
        function checkIn(recordId) {
            $.modal.confirm("确认要为该用户签到吗？", function() {
                $.operate.post(prefix + "/record/checkin", { "recordId": recordId });
            })
        }

        /* 取消预约 */
        function cancelRecord(recordId) {
            $.modal.prompt("请输入取消原因", function(value) {
                $.operate.post(prefix + "/record/cancel", { "recordId": recordId, "cancelReason": value });
            });
        }

        /* 查看详情 */
        function showDetail(recordId) {
            var url = ctx + "system/record/detail/" + recordId;
            $.modal.openTab("预约详情", url);
        }

        /* 刷新统计 */
        function refreshStats() {
            $.get(ctx + "system/record/stats/" + reservationId, function(result) {
                if (result.code == web_status.SUCCESS) {
                    $.modal.msgSuccess("统计数据已刷新");
                    location.reload();
                } else {
                    $.modal.msgError(result.msg);
                }
            });
        }
    </script>
</body>
</html>
