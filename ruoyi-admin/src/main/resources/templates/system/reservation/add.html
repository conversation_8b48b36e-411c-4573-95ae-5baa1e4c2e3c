<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增预约管理')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-reservation-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">预约编码：</label>
                <div class="col-sm-8">
                    <input name="reservationCode" class="form-control" type="text" placeholder="请输入预约编码" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">预约名称：</label>
                <div class="col-sm-8">
                    <input name="reservationName" class="form-control" type="text" placeholder="请输入预约名称" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">场地：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input name="venueName" class="form-control" type="text" placeholder="请选择场地" readonly required>
                        <input name="venueId" type="hidden">
                        <span class="input-group-addon"><i class="fa fa-search cursor-pointer" onclick="selectVenue()"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">体测日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="testDate" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">开始时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="startTime" class="form-control" placeholder="HH:mm:ss" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">结束时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="endTime" class="form-control" placeholder="HH:mm:ss" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">男生名额：</label>
                <div class="col-sm-8">
                    <input name="maleQuota" class="form-control" type="number" placeholder="请输入男生名额" min="0" value="0">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">女生名额：</label>
                <div class="col-sm-8">
                    <input name="femaleQuota" class="form-control" type="number" placeholder="请输入女生名额" min="0" value="0">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">预约类型：</label>
                <div class="col-sm-8">
                    <select name="reservationType" class="form-control" th:with="type=${@dict.getType('reservation_type')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{reservationType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">测试项目：</label>
                <div class="col-sm-8">
                    <textarea name="testItems" class="form-control" placeholder="请输入测试项目，多个项目用逗号分隔"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">预约要求：</label>
                <div class="col-sm-8">
                    <textarea name="requirements" class="form-control" placeholder="请输入预约要求"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系人：</label>
                <div class="col-sm-8">
                    <input name="contactPerson" class="form-control" type="text" placeholder="请输入联系人">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系电话：</label>
                <div class="col-sm-8">
                    <input name="contactPhone" class="form-control" type="text" placeholder="请输入联系电话">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否开放：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="isOpen1" name="isOpen" value="1" checked>
                        <label for="isOpen1">开放</label>
                        <input type="radio" id="isOpen0" name="isOpen" value="0">
                        <label for="isOpen0">关闭</label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">显示顺序：</label>
                <div class="col-sm-8">
                    <input name="orderNum" class="form-control" type="number" placeholder="请输入显示顺序" value="0">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                        <input type="radio" th:id="${'status' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'status' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" placeholder="请输入备注"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/reservation";
        $("#form-reservation-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-reservation-add').serialize());
            }
        }

        $("input[name='testDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            language: 'zh-CN',
            autoclose: true
        });

        $("input[name='startTime']").datetimepicker({
            format: "hh:ii:ss",
            startView: 1,
            maxView: 1,
            language: 'zh-CN',
            autoclose: true
        });

        $("input[name='endTime']").datetimepicker({
            format: "hh:ii:ss",
            startView: 1,
            maxView: 1,
            language: 'zh-CN',
            autoclose: true
        });

        // 选择场地
        function selectVenue() {
            var options = {
                title: '选择场地',
                width: "400",
                url: prefix + "/selectVenueTree",
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        function doSubmit(index, layero){
            var body = $.modal.getChildFrame(index);
            $("#venueId").val(body.find('#venueId').val());
            $("#venueName").val(body.find('#venueName').val());
            $.modal.close(index);
        }

        // 自动计算总名额
        $("input[name='maleQuota'], input[name='femaleQuota']").on('input', function() {
            var maleQuota = parseInt($("input[name='maleQuota']").val()) || 0;
            var femaleQuota = parseInt($("input[name='femaleQuota']").val()) || 0;
            var totalQuota = maleQuota + femaleQuota;
            $("input[name='totalQuota']").val(totalQuota);
        });
    </script>
</body>
</html>
