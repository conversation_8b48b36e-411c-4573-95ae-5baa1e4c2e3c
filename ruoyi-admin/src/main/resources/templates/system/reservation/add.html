<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('发布新的体测预约')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="alert alert-info">
            <i class="fa fa-info-circle"></i>
            <strong>发布体测预约说明：</strong>
            填写完整的预约信息后，可以选择立即发布或稍后发布。发布后，学生将能够看到此预约并进行预约操作。
        </div>

        <form class="form-horizontal m" id="form-reservation-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约编码：</label>
                    <div class="col-sm-8">
                        <input name="reservationCode" class="form-control" type="text" placeholder="系统自动生成或手动输入">
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 用于系统内部识别，可留空自动生成</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">预约名称：</label>
                    <div class="col-sm-8">
                        <input name="reservationName" class="form-control" type="text" placeholder="例如：2024年春季体质测试" required>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 学生看到的预约标题</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">场地ID：</label>
                    <div class="col-sm-8">
                        <input name="venueId" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">场地名称：</label>
                    <div class="col-sm-8">
                        <input name="venueName" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">体测日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="testDate" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">开始时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="startTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">结束时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="endTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">男生名额：</label>
                    <div class="col-sm-8">
                        <input name="maleQuota" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">女生名额：</label>
                    <div class="col-sm-8">
                        <input name="femaleQuota" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">男生已预约：</label>
                    <div class="col-sm-8">
                        <input name="maleReserved" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">女生已预约：</label>
                    <div class="col-sm-8">
                        <input name="femaleReserved" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">总名额：</label>
                    <div class="col-sm-8">
                        <input name="totalQuota" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">总已预约：</label>
                    <div class="col-sm-8">
                        <input name="totalReserved" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">测试项目：</label>
                    <div class="col-sm-8">
                        <textarea name="testItems" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约要求：</label>
                    <div class="col-sm-8">
                        <textarea name="requirements" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">联系人：</label>
                    <div class="col-sm-8">
                        <input name="contactPerson" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">联系电话：</label>
                    <div class="col-sm-8">
                        <input name="contactPhone" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">删除标志：</label>
                    <div class="col-sm-8">
                        <input name="delFlag" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/reservation"
        $("#form-reservation-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-reservation-add').serialize());
            }
        }

        $("input[name='testDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='startTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='endTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>