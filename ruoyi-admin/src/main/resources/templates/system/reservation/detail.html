<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('预约详情')" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <!-- 预约基本信息 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5><i class="fa fa-calendar"></i> 体测预约详情</h5>
                        <div class="ibox-tools">
                            <span class="label" th:classappend="${reservation.isOpen == '1'} ? 'label-success' : 'label-default'">
                                <span th:text="${reservation.isOpen == '1'} ? '已发布' : '未发布'"></span>
                            </span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">预约名称：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${reservation.reservationName}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">预约编码：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${reservation.reservationCode}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">体测日期：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static text-primary" th:text="${#dates.format(reservation.testDate, 'yyyy-MM-dd')}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">时间段：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static text-info">
                                            <span th:text="${#dates.format(reservation.startTime, 'HH:mm:ss')}"></span>
                                            -
                                            <span th:text="${#dates.format(reservation.endTime, 'HH:mm:ss')}"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">体测场地：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${reservation.venueName}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">预约类型：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static">
                                            <span class="label label-primary" th:text="${reservation.reservationType}"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">预约名额：</label>
                                    <div class="col-sm-10">
                                        <div class="progress-group">
                                            <span class="progress-text">男生名额</span>
                                            <span class="float-right"><b th:text="${reservation.maleReserved ?: 0}"></b>/<span th:text="${reservation.maleQuota ?: 0}"></span></span>
                                            <div class="progress progress-mini">
                                                <div class="progress-bar progress-bar-primary" 
                                                     th:style="'width: ' + ${reservation.maleQuota > 0 ? (reservation.maleReserved * 100 / reservation.maleQuota) : 0} + '%'"></div>
                                            </div>
                                        </div>
                                        <div class="progress-group">
                                            <span class="progress-text">女生名额</span>
                                            <span class="float-right"><b th:text="${reservation.femaleReserved ?: 0}"></b>/<span th:text="${reservation.femaleQuota ?: 0}"></span></span>
                                            <div class="progress progress-mini">
                                                <div class="progress-bar progress-bar-danger" 
                                                     th:style="'width: ' + ${reservation.femaleQuota > 0 ? (reservation.femaleReserved * 100 / reservation.femaleQuota) : 0} + '%'"></div>
                                            </div>
                                        </div>
                                        <div class="progress-group">
                                            <span class="progress-text">总名额</span>
                                            <span class="float-right"><b th:text="${reservation.totalReserved ?: 0}"></b>/<span th:text="${reservation.totalQuota ?: 0}"></span></span>
                                            <div class="progress progress-mini">
                                                <div class="progress-bar progress-bar-success" 
                                                     th:style="'width: ' + ${reservation.totalQuota > 0 ? (reservation.totalReserved * 100 / reservation.totalQuota) : 0} + '%'"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${reservation.testItems}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">测试项目：</label>
                                    <div class="col-sm-10">
                                        <div class="well well-sm" th:text="${reservation.testItems}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${reservation.requirements}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">预约要求：</label>
                                    <div class="col-sm-10">
                                        <div class="well well-sm" th:text="${reservation.requirements}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${reservation.contactPerson or reservation.contactPhone}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">联系方式：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static">
                                            <i class="fa fa-user"></i> <span th:if="${reservation.contactPerson}" th:text="${reservation.contactPerson}"></span>
                                            <i class="fa fa-phone" th:if="${reservation.contactPhone}"></i> <span th:if="${reservation.contactPhone}" th:text="${reservation.contactPhone}"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${reservation.remark}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">备注信息：</label>
                                    <div class="col-sm-10">
                                        <div class="well well-sm" th:text="${reservation.remark}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="row">
                            <div class="col-sm-12 text-center">
                                <div class="btn-group">
                                    <!-- 学生预约按钮 -->
                                    <button type="button" class="btn btn-success" th:if="${reservation.isOpen == '1'}" 
                                            onclick="studentReservation()">
                                        <i class="fa fa-user-plus"></i> 立即预约
                                    </button>
                                    
                                    <!-- 管理员操作按钮 -->
                                    <div shiro:hasRole="admin">
                                        <button type="button" class="btn btn-primary" onclick="editReservation()" 
                                                shiro:hasPermission="system:reservation:edit">
                                            <i class="fa fa-edit"></i> 编辑预约
                                        </button>
                                        <button type="button" class="btn btn-warning" th:if="${reservation.isOpen == '1'}" 
                                                onclick="closeReservation()" shiro:hasPermission="system:reservation:edit">
                                            <i class="fa fa-pause"></i> 取消发布
                                        </button>
                                        <button type="button" class="btn btn-success" th:if="${reservation.isOpen != '1'}" 
                                                onclick="openReservation()" shiro:hasPermission="system:reservation:edit">
                                            <i class="fa fa-play"></i> 发布预约
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="cloneReservation()" 
                                                shiro:hasPermission="system:reservation:add">
                                            <i class="fa fa-copy"></i> 克隆预约
                                        </button>
                                    </div>
                                    
                                    <!-- 返回按钮 -->
                                    <button type="button" class="btn btn-default" onclick="$.modal.closeTab()">
                                        <i class="fa fa-reply"></i> 返回
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/reservation";
        var reservationId = [[${reservation.reservationId}]];

        // 学生预约
        function studentReservation() {
            var url = prefix + "/studentReservation/" + reservationId;
            $.modal.openTab("学生预约", url);
        }

        // 编辑预约
        function editReservation() {
            var url = prefix + "/edit/" + reservationId;
            $.modal.openTab("编辑预约", url);
        }

        // 发布预约
        function openReservation() {
            $.modal.confirm("确认要发布这个预约吗？发布后学生可以看到并进行预约。", function() {
                $.ajax({
                    url: prefix + "/open/" + reservationId,
                    type: "POST",
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess("发布成功");
                            location.reload();
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    }
                });
            });
        }

        // 取消发布
        function closeReservation() {
            $.modal.confirm("确认要取消发布这个预约吗？取消后学生将无法看到此预约。", function() {
                $.ajax({
                    url: prefix + "/close/" + reservationId,
                    type: "POST",
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess("取消发布成功");
                            location.reload();
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    }
                });
            });
        }

        // 克隆预约
        function cloneReservation() {
            $.modal.confirm("确认要克隆这个预约设置吗？将创建一个相同配置的新预约。", function() {
                $.ajax({
                    url: prefix + "/clone/" + reservationId,
                    type: "POST",
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess("克隆成功");
                            $.modal.closeTab();
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    }
                });
            });
        }
    </script>
</body>
</html>
