<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改预约记录')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-record-edit" th:object="${sysReservationRecord}">
            <input name="recordId" th:field="*{recordId}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">预约ID：</label>
                    <div class="col-sm-8">
                        <input name="reservationId" th:field="*{reservationId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">用户ID：</label>
                    <div class="col-sm-8">
                        <input name="userId" th:field="*{userId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">用户姓名：</label>
                    <div class="col-sm-8">
                        <input name="userName" th:field="*{userName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">学号：</label>
                    <div class="col-sm-8">
                        <input name="studentId" th:field="*{studentId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">性别：</label>
                    <div class="col-sm-8">
                        <input name="gender" th:field="*{gender}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">手机号码：</label>
                    <div class="col-sm-8">
                        <input name="phone" th:field="*{phone}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">邮箱：</label>
                    <div class="col-sm-8">
                        <input name="email" th:field="*{email}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">院系名称：</label>
                    <div class="col-sm-8">
                        <input name="deptName" th:field="*{deptName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">班级名称：</label>
                    <div class="col-sm-8">
                        <input name="className" th:field="*{className}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预约时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="reservationTime" th:value="${#dates.format(sysReservationRecord.reservationTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">签到时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="checkInTime" th:value="${#dates.format(sysReservationRecord.checkInTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">是否签到：</label>
                    <div class="col-sm-8">
                        <input name="isCheckedIn" th:field="*{isCheckedIn}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">测试结果：</label>
                    <div class="col-sm-8">
                        <textarea name="testResult" class="form-control">[[*{testResult}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">取消原因：</label>
                    <div class="col-sm-8">
                        <input name="cancelReason" th:field="*{cancelReason}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">取消时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="cancelTime" th:value="${#dates.format(sysReservationRecord.cancelTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/record";
        $("#form-record-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-record-edit').serialize());
            }
        }

        $("input[name='reservationTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='checkInTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='cancelTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>