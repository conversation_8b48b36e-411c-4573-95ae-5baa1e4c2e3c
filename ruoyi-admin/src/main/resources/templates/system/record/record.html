<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('预约记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>预约ID：</label>
                                <input type="text" name="reservationId"/>
                            </li>
                            <li>
                                <label>用户ID：</label>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <label>用户姓名：</label>
                                <input type="text" name="userName"/>
                            </li>
                            <li>
                                <label>学号：</label>
                                <input type="text" name="studentId"/>
                            </li>
                            <li>
                                <label>性别：</label>
                                <input type="text" name="gender"/>
                            </li>
                            <li>
                                <label>手机号码：</label>
                                <input type="text" name="phone"/>
                            </li>
                            <li>
                                <label>邮箱：</label>
                                <input type="text" name="email"/>
                            </li>
                            <li>
                                <label>院系名称：</label>
                                <input type="text" name="deptName"/>
                            </li>
                            <li>
                                <label>班级名称：</label>
                                <input type="text" name="className"/>
                            </li>
                            <li>
                                <label>预约时间：</label>
                                <input type="text" class="time-input" placeholder="请选择预约时间" name="reservationTime"/>
                            </li>
                            <li>
                                <label>签到时间：</label>
                                <input type="text" class="time-input" placeholder="请选择签到时间" name="checkInTime"/>
                            </li>
                            <li>
                                <label>是否签到：</label>
                                <input type="text" name="isCheckedIn"/>
                            </li>
                            <li>
                                <label>取消原因：</label>
                                <input type="text" name="cancelReason"/>
                            </li>
                            <li>
                                <label>取消时间：</label>
                                <input type="text" class="time-input" placeholder="请选择取消时间" name="cancelTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:record:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:record:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:record:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:record:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:record:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:record:remove')}]];
        var prefix = ctx + "system/record";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "预约记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'recordId',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'reservationId',
                    title: '预约ID'
                },
                {
                    field: 'userId',
                    title: '用户ID'
                },
                {
                    field: 'userName',
                    title: '用户姓名'
                },
                {
                    field: 'studentId',
                    title: '学号'
                },
                {
                    field: 'gender',
                    title: '性别'
                },
                {
                    field: 'phone',
                    title: '手机号码'
                },
                {
                    field: 'email',
                    title: '邮箱'
                },
                {
                    field: 'deptName',
                    title: '院系名称'
                },
                {
                    field: 'className',
                    title: '班级名称'
                },
                {
                    field: 'reservationTime',
                    title: '预约时间'
                },
                {
                    field: 'checkInTime',
                    title: '签到时间'
                },
                {
                    field: 'isCheckedIn',
                    title: '是否签到'
                },
                {
                    field: 'testResult',
                    title: '测试结果'
                },
                {
                    field: 'recordStatus',
                    title: '记录状态'
                },
                {
                    field: 'cancelReason',
                    title: '取消原因'
                },
                {
                    field: 'cancelTime',
                    title: '取消时间'
                },
                {
                    field: 'status',
                    title: '状态'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.recordId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.recordId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>