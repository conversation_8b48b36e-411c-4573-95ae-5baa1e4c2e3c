<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改场地管理')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-venue-edit" th:object="${sysVenue}">
            <input name="venueId" th:field="*{venueId}" type="hidden">

            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">场地名称：</label>
                    <div class="col-sm-8">
                        <input name="venueName" th:field="*{venueName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>


            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">场地位置：</label>
                    <div class="col-sm-8">
                        <input name="location" th:field="*{location}" class="form-control" type="text">
                    </div>
                </div>
            </div>




            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">联系人：</label>
                    <div class="col-sm-8">
                        <input name="contactPerson" th:field="*{contactPerson}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">联系电话：</label>
                    <div class="col-sm-8">
                        <input name="contactPhone" th:field="*{contactPhone}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">场地描述：</label>
                    <div class="col-sm-8">
                        <textarea name="description" class="form-control">[[*{description}]]</textarea>
                    </div>
                </div>
            </div>


            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/venue";
        $("#form-venue-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-venue-edit').serialize());
            }
        }
    </script>
</body>
</html>