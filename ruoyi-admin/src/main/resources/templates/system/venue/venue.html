<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('场地管理列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>场地编码：</label>
                                <input type="text" name="venueCode"/>
                            </li>
                            <li>
                                <label>场地名称：</label>
                                <input type="text" name="venueName"/>
                            </li>


                            <li>
                                <label>场地位置：</label>
                                <input type="text" name="location"/>
                            </li>


                            <li>
                                <label>联系人：</label>
                                <input type="text" name="contactPerson"/>
                            </li>
                            <li>
                                <label>联系电话：</label>
                                <input type="text" name="contactPhone"/>
                            </li>
                            <li>
                                <label>显示顺序：</label>
                                <input type="text" name="orderNum"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:venue:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:venue:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:venue:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:venue:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:venue:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:venue:remove')}]];
        var prefix = ctx + "system/venue";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "场地管理",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'venueId',
                    title: '场地ID',
                    visible: false
                },
                {
                    field: 'venueCode',
                    title: '场地编码'
                },
                {
                    field: 'venueName',
                    title: '场地名称'
                },
                {
                    field: 'venueType',
                    title: '场地类型'
                },
                {
                    field: 'capacity',
                    title: '容纳人数'
                },
                {
                    field: 'area',
                    title: '场地面积'
                },
                {
                    field: 'location',
                    title: '场地位置'
                },
                {
                    field: 'floor',
                    title: '所在楼层'
                },
                {
                    field: 'equipment',
                    title: '设备设施'
                },
                {
                    field: 'priceHour',
                    title: '每小时价格'
                },
                {
                    field: 'priceDay',
                    title: '每天价格'
                },
                {
                    field: 'contactPerson',
                    title: '联系人'
                },
                {
                    field: 'contactPhone',
                    title: '联系电话'
                },
                {
                    field: 'description',
                    title: '场地描述'
                },

                {
                    field: 'orderNum',
                    title: '显示顺序'
                },

                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.venueId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.venueId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>