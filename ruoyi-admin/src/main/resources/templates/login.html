<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>大学生体质测试管理系统</title>
    <meta name="description" content="大学生体质测试管理系统 - 专业的学生体质健康数据管理平台">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.8.1}" rel="stylesheet"/>
    <!-- 自定义登录页面样式 -->
    <link href="../static/css/fitness-login.css" th:href="@{/css/fitness-login.css}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>

    <style type="text/css">
        label.error { position:inherit; }

        /* PC端专业登录页面样式 */
        body.signin {
            background: #f5f7fa;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            min-height: 100vh;
            position: relative;
        }

        /* 背景装饰 */
        body.signin::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: -2;
        }

        body.signin::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
        }

        .signinpanel {
            max-width: 1200px;
            margin: 80px auto;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.8);
        }

        .signin-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 60px;
            position: relative;
            min-height: 600px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .signin-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="90" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .logopanel {
            text-align: center;
            position: relative;
            z-index: 2;
            margin-bottom: 40px;
        }

        .logo-icon {
            font-size: 72px;
            color: #ffffff;
            margin-bottom: 20px;
            display: inline-block;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .system-title {
            font-size: 32px;
            font-weight: 300;
            margin: 0;
            color: #ffffff;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            letter-spacing: 1px;
        }

        .welcome-text {
            font-size: 20px;
            font-weight: 300;
            margin: 40px 0;
            position: relative;
            z-index: 2;
            text-align: center;
            opacity: 0.95;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            position: relative;
            z-index: 2;
            margin: 40px 0;
        }

        .feature-list li {
            padding: 12px 0;
            font-size: 16px;
            opacity: 0.9;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .feature-list li:hover {
            opacity: 1;
            transform: translateX(8px);
        }

        .feature-list i {
            color: #ffffff;
            margin-right: 15px;
            width: 20px;
            font-size: 18px;
        }

        /* 登录表单样式 */
        .login-form {
            padding: 80px 60px;
            background: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 600px;
        }

        .login-title {
            font-size: 36px;
            font-weight: 300;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
            letter-spacing: 1px;
        }

        .login-subtitle {
            color: #95a5a6;
            text-align: center;
            margin-bottom: 50px;
            font-size: 16px;
            font-weight: 300;
        }

        .form-group {
            margin-bottom: 30px;
            position: relative;
        }

        .form-control {
            height: 56px;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 18px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #ffffff;
            width: 100%;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .form-control::placeholder {
            color: #bdc3c7;
            font-weight: 300;
        }

        .btn-login {
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 400;
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
            letter-spacing: 0.5px;
        }

        .btn-login:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .captcha-container {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .captcha-input {
            flex: 1;
        }

        .captcha-image {
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #ddd;
        }

        .captcha-image:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .checkbox-custom {
            display: flex;
            align-items: center;
            margin: 25px 0;
            font-size: 14px;
            color: #7f8c8d;
        }

        .checkbox-custom input[type="checkbox"] {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }

        .register-link {
            text-align: center;
            margin-top: 30px;
            position: relative;
            z-index: 2;
        }

        .register-link a {
            color: #ffffff;
            text-decoration: none;
            font-weight: 300;
            transition: all 0.3s ease;
            opacity: 0.9;
        }

        .register-link a:hover {
            opacity: 1;
            text-decoration: underline;
        }

        .signup-footer {
            background: #f8f9fa;
            padding: 25px;
            text-align: center;
            color: #95a5a6;
            font-size: 14px;
            border-top: 1px solid #ecf0f1;
        }

        /* PC端优化 */
        @media (min-width: 1200px) {
            .signinpanel {
                max-width: 1400px;
            }

            .signin-info {
                padding: 100px 80px;
            }

            .login-form {
                padding: 100px 80px;
            }
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .signinpanel {
                margin: 40px 20px;
                max-width: none;
            }

            .signin-info, .login-form {
                padding: 60px 40px;
                min-height: auto;
            }
        }

        @media (max-width: 768px) {
            .signinpanel {
                margin: 20px;
                border-radius: 8px;
            }

            .signin-info, .login-form {
                padding: 40px 30px;
            }

            .system-title {
                font-size: 28px;
            }

            .login-title {
                font-size: 32px;
            }

            .row {
                margin: 0;
            }

            .col-sm-7, .col-sm-5 {
                padding: 0;
            }
        }

        /* 错误提示样式 */
        label.error {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        /* 加载状态 */
        .btn-login[disabled] {
            opacity: 0.7;
            cursor: not-allowed;
        }
    </style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body class="signin">
    <div class="signinpanel">
        <div class="row">
            <div class="col-sm-7">
                <div class="signin-info">
                    <div class="logopanel">
                        <div class="logo-icon">
                            <i class="fa fa-heartbeat"></i>
                        </div>
                        <h1 class="system-title">大学生体质测试管理系统</h1>
                    </div>

                    <p class="welcome-text">专业的学生体质健康数据管理平台</p>

                    <ul class="feature-list">
                        <li><i class="fa fa-database"></i> 体质测试数据管理</li>
                        <li><i class="fa fa-user-md"></i> 学生健康档案建立</li>
                        <li><i class="fa fa-bar-chart"></i> 测试成绩统计分析</li>
                        <li><i class="fa fa-file-text-o"></i> 数据分析报告生成</li>
                        <li><i class="fa fa-users"></i> 多角色权限管理</li>
                        <li><i class="fa fa-shield"></i> 数据安全保障</li>
                    </ul>

                    <div class="register-link" th:if="${isAllowRegister}">
                        <p>还没有账号？ <a th:href="@{/register}">申请账号</a></p>
                    </div>
                </div>
            </div>
            <div class="col-sm-5">
                <div class="login-form">
                    <form id="signupForm" autocomplete="off">
                        <h2 class="login-title">用户登录</h2>
                        <p class="login-subtitle">请输入您的登录凭据</p>

                        <div class="form-group">
                            <input type="text" name="username" class="form-control" placeholder="用户名" value="admin" />
                        </div>

                        <div class="form-group">
                            <input type="password" name="password" class="form-control" placeholder="密码" value="admin123" />
                        </div>

                        <div class="form-group captcha-container" th:if="${captchaEnabled==true}">
                            <div class="captcha-input">
                                <input type="text" name="validateCode" class="form-control" placeholder="验证码" maxlength="5" />
                            </div>
                            <div>
                                <a href="javascript:void(0);" title="点击更换验证码">
                                    <img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="captcha-image" width="120" height="56"/>
                                </a>
                            </div>
                        </div>

                        <div class="checkbox-custom" th:if="${isRemembered}">
                            <input type="checkbox" id="rememberme" name="rememberme">
                            <label for="rememberme">记住登录状态</label>
                        </div>

                        <button class="btn btn-login btn-block" id="btnSubmit" data-loading="正在验证登录，请稍候...">
                            登录系统
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <div class="signup-footer">
            <div>
                <p style="margin: 0;">
                    <i class="fa fa-copyright"></i> 2025 大学生体质测试管理系统 All Rights Reserved.
                </p>
                <p style="margin: 5px 0 0 0; opacity: 0.8;">
                    <i class="fa fa-heart" style="color: #e74c3c;"></i> 致力于提升大学生体质健康水平
                </p>
            </div>
        </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.1}"></script>
<script src="../static/ruoyi/login.js" th:src="@{/ruoyi/login.js}"></script>
<script>
$(document).ready(function() {
    // 简洁的表单交互
    $('.form-control').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        if (!$(this).val()) {
            $(this).parent().removeClass('focused');
        }
    });

    // 登录按钮状态
    $('#btnSubmit').on('click', function() {
        var $btn = $(this);
        if (!$btn.hasClass('disabled')) {
            $btn.html('正在登录...');
            $btn.prop('disabled', true);
        }
    });

    // 键盘快捷键支持
    $(document).on('keydown', function(e) {
        if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
            var $focused = $(':focus');
            if ($focused.hasClass('form-control')) {
                $('#btnSubmit').click();
            }
        }
    });

    // 验证码刷新
    $('.captcha-image').on('click', function() {
        var $img = $(this);
        var src = $img.attr('src');
        $img.attr('src', src + '&t=' + new Date().getTime());
    });
});
</script>
</body>
</html>
