<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>大学生体质测试管理系统</title>
    <meta name="description" content="大学生体质测试管理系统 - 国家学生体质健康标准测试管理平台">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.8.1}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">label.error { position:inherit;  }</style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body class="signin">
    <div class="signinpanel">
        <div class="row">
            <div class="col-sm-7">
                <div class="signin-info">
                    <div class="logopanel m-b">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <i class="fa fa-heartbeat" style="font-size: 48px; color: #ffffff; margin-bottom: 15px; text-shadow: 0 2px 4px rgba(0,0,0,0.3);"></i>
                            <h2 style="color: #ffffff; margin: 0; font-weight: 400; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">大学生体质测试管理系统</h2>
                            <p style="color: #ffffff; font-size: 14px; margin-top: 5px; opacity: 0.95; text-shadow: 0 1px 2px rgba(0,0,0,0.2);">Student Physical Fitness Testing System</p>
                        </div>
                    </div>
                    <div class="m-b"></div>
                    <h4 style="color: #ffffff; text-align: center; margin-bottom: 20px; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">🏃‍♂️ 健康体魄，从测试开始</h4>
                    <p style="color: #ffffff; text-align: center; margin-bottom: 25px; font-size: 15px; opacity: 0.95; text-shadow: 0 1px 2px rgba(0,0,0,0.2);">国家学生体质健康标准测试管理平台</p>

                    <div style="background: rgba(255,255,255,0.15); border-radius: 8px; padding: 20px; margin-bottom: 20px; border: 1px solid rgba(255,255,255,0.2);">
                        <h5 style="color: #ffffff; margin-bottom: 15px; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">📊 系统功能</h5>
                        <ul class="m-b" style="list-style: none; padding: 0;">
                            <li style="color: #ffffff; padding: 6px 0; font-size: 14px; text-shadow: 0 1px 2px rgba(0,0,0,0.2);"><i class="fa fa-user-plus m-r-xs" style="color: #ffffff;"></i> 学生信息管理与注册</li>
                            <li style="color: #ffffff; padding: 6px 0; font-size: 14px; text-shadow: 0 1px 2px rgba(0,0,0,0.2);"><i class="fa fa-calendar m-r-xs" style="color: #ffffff;"></i> 体测时间安排预约</li>
                            <li style="color: #ffffff; padding: 6px 0; font-size: 14px; text-shadow: 0 1px 2px rgba(0,0,0,0.2);"><i class="fa fa-heartbeat m-r-xs" style="color: #ffffff;"></i> 体质健康数据录入</li>
                            <li style="color: #ffffff; padding: 6px 0; font-size: 14px; text-shadow: 0 1px 2px rgba(0,0,0,0.2);"><i class="fa fa-bar-chart m-r-xs" style="color: #ffffff;"></i> 成绩统计分析报告</li>
                            <li style="color: #ffffff; padding: 6px 0; font-size: 14px; text-shadow: 0 1px 2px rgba(0,0,0,0.2);"><i class="fa fa-trophy m-r-xs" style="color: #ffffff;"></i> 体质等级评定</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.15); border-radius: 8px; padding: 15px; margin-bottom: 20px; border: 1px solid rgba(255,255,255,0.2);">
                        <h6 style="color: #ffffff; margin-bottom: 10px; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">🎯 测试项目（国标）</h6>
                        <div style="display: flex; flex-wrap: wrap; gap: 6px;">
                            <span style="background: rgba(255,255,255,0.3); padding: 4px 10px; border-radius: 12px; font-size: 12px; color: #ffffff; text-shadow: 0 1px 1px rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.3);">身高体重BMI</span>
                            <span style="background: rgba(255,255,255,0.3); padding: 4px 10px; border-radius: 12px; font-size: 12px; color: #ffffff; text-shadow: 0 1px 1px rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.3);">肺活量</span>
                            <span style="background: rgba(255,255,255,0.3); padding: 4px 10px; border-radius: 12px; font-size: 12px; color: #ffffff; text-shadow: 0 1px 1px rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.3);">50米跑</span>
                            <span style="background: rgba(255,255,255,0.3); padding: 4px 10px; border-radius: 12px; font-size: 12px; color: #ffffff; text-shadow: 0 1px 1px rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.3);">立定跳远</span>
                            <span style="background: rgba(255,255,255,0.3); padding: 4px 10px; border-radius: 12px; font-size: 12px; color: #ffffff; text-shadow: 0 1px 1px rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.3);">坐位体前屈</span>
                            <span style="background: rgba(255,255,255,0.3); padding: 4px 10px; border-radius: 12px; font-size: 12px; color: #ffffff; text-shadow: 0 1px 1px rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.3);">仰卧起坐/引体向上</span>
                            <span style="background: rgba(255,255,255,0.3); padding: 4px 10px; border-radius: 12px; font-size: 12px; color: #ffffff; text-shadow: 0 1px 1px rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.3);">800/1000米跑</span>
                        </div>
                    </div>

                    <div style="text-align: center;" th:if="${isAllowRegister}">
                        <strong style="color: #ffffff; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">🎓 学生用户？ <a th:href="@{/register}" style="color: #ffffff; text-decoration: underline; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">立即注册账号 &raquo;</a></strong>
                    </div>
                </div>
            </div>
            <div class="col-sm-5">
                <form id="signupForm" autocomplete="off">
                    <h3 style="text-align: center; color: #2c3e50; margin-bottom: 10px; font-weight: 300;">系统登录</h3>
                    <p style="text-align: center; color: #7f8c8d; margin-bottom: 30px; font-size: 14px;">请输入您的登录凭据</p>

                    <input type="text" name="username" class="form-control uname" placeholder="👤 学号/工号" value="admin" style="margin-bottom: 15px; height: 45px; padding: 12px 15px;" />
                    <input type="password" name="password" class="form-control pword" placeholder="🔒 密码" value="admin123" style="margin-bottom: 15px; height: 45px; padding: 12px 15px;" />

					<div class="row m-t" th:if="${captchaEnabled==true}" style="margin-bottom: 15px;">
						<div class="col-xs-6">
						    <input type="text" name="validateCode" class="form-control code" placeholder="🔢 验证码" maxlength="5" style="height: 45px; padding: 12px 15px;" />
						</div>
						<div class="col-xs-6">
							<a href="javascript:void(0);" title="点击更换验证码">
								<img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="imgcode" width="85%" style="height: 45px; border-radius: 4px; border: 1px solid #ddd;"/>
							</a>
						</div>
					</div>

                    <div class="checkbox-custom" th:if="${isRemembered}" th:classappend="${captchaEnabled==false} ? 'm-t'" style="margin-bottom: 20px;">
				        <input type="checkbox" id="rememberme" name="rememberme"> <label for="rememberme" style="color: #7f8c8d; font-size: 14px;">记住登录状态</label>
				    </div>

                    <button class="btn btn-success btn-block" id="btnSubmit" data-loading="正在验证登录，请稍候..." style="height: 45px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; font-size: 16px; margin-bottom: 15px;">
                        🚀 登录系统
                    </button>

                    <!-- 注册按钮区域 -->
                    <div style="text-align: center; margin-bottom: 20px;" th:if="${isAllowRegister}">
                        <button type="button" onclick="window.location.href='/register'"
                                style="width: 100%; height: 45px; background: transparent; border: 2px solid #667eea; color: #667eea; border-radius: 6px; font-size: 16px; cursor: pointer; transition: all 0.3s ease;"
                                onmouseover="this.style.background='#667eea'; this.style.color='white'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.3)';"
                                onmouseout="this.style.background='transparent'; this.style.color='#667eea'; this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                            📝 学生注册
                        </button>
                        <p style="color: #7f8c8d; font-size: 12px; margin: 8px 0 0 0;">
                            🎓 还没有账号？点击注册学生账号
                        </p>
                    </div>

                    <div style="text-align: center; padding-top: 15px; border-top: 1px solid #eee;">
                        <p style="color: #6c757d; font-size: 12px; margin: 0; line-height: 1.4;">
                            💡 提示：学号/工号作为用户名登录<br>
                            管理员账号：admin/admin123
                        </p>
                    </div>
                </form>
            </div>
        </div>
        <div class="signup-footer">
            <div class="pull-left">
                Copyright © 2025 大学生体质测试管理系统 All Rights Reserved. <br>
                <small style="opacity: 0.8;">致力于提升大学生体质健康水平 • 科学测试 数据驱动</small>
            </div>
        </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.1}"></script>
<script src="../static/ruoyi/login.js" th:src="@{/ruoyi/login.js}"></script>
</body>
</html>
