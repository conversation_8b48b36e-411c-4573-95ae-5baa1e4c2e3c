<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>大学生体质测试管理系统</title>
    <meta name="description" content="大学生体质测试管理系统 - 专业的学生体质健康数据管理平台">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.8.1}" rel="stylesheet"/>
    <!-- 自定义登录页面样式 -->
    <link href="../static/css/fitness-login.css" th:href="@{/css/fitness-login.css}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style type="text/css">
        label.error { position:inherit; }

        /* 自定义样式 */
        body.signin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Poppins', sans-serif;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* 动态背景粒子效果 */
        body.signin::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .signinpanel {
            position: relative;
            z-index: 10;
            margin-top: 5%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .signin-info {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 60px 40px;
            position: relative;
            overflow: hidden;
        }

        .signin-info::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .logopanel {
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .logo-icon {
            font-size: 64px;
            color: #4facfe;
            margin-bottom: 20px;
            display: inline-block;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .system-title {
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-text {
            font-size: 18px;
            font-weight: 500;
            margin: 30px 0;
            position: relative;
            z-index: 2;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            position: relative;
            z-index: 2;
        }

        .feature-list li {
            padding: 8px 0;
            font-size: 14px;
            opacity: 0.9;
            transition: all 0.3s ease;
        }

        .feature-list li:hover {
            opacity: 1;
            transform: translateX(5px);
        }

        .feature-list i {
            color: #4facfe;
            margin-right: 10px;
            width: 16px;
        }

        /* 登录表单样式 */
        .login-form {
            padding: 60px 40px;
            background: white;
        }

        .login-title {
            font-size: 32px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            text-align: center;
        }

        .login-subtitle {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 40px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-control {
            height: 50px;
            border: 2px solid #e8ecef;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            background: white;
            outline: none;
        }

        .btn-login {
            height: 50px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
        }

        .captcha-container {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .captcha-input {
            flex: 1;
        }

        .captcha-image {
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .captcha-image:hover {
            transform: scale(1.05);
        }

        .checkbox-custom {
            display: flex;
            align-items: center;
            margin: 20px 0;
        }

        .checkbox-custom input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .register-link {
            text-align: center;
            margin-top: 20px;
        }

        .register-link a {
            color: #4facfe;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .register-link a:hover {
            color: #2980b9;
        }

        .signup-footer {
            background: rgba(0,0,0,0.05);
            padding: 20px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .signinpanel {
                margin: 20px;
                border-radius: 15px;
            }

            .signin-info, .login-form {
                padding: 40px 30px;
            }

            .system-title {
                font-size: 24px;
            }

            .login-title {
                font-size: 28px;
            }

            .row {
                margin: 0;
            }

            .col-sm-7, .col-sm-5 {
                padding: 0;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .signin-info {
                padding: 30px 20px;
            }

            .login-form {
                padding: 30px 20px;
            }

            .system-title {
                font-size: 20px;
            }

            .login-title {
                font-size: 24px;
            }

            .feature-list {
                font-size: 13px;
            }

            .form-control {
                height: 45px;
                font-size: 14px;
            }

            .btn-login {
                height: 45px;
                font-size: 14px;
            }
        }
    </style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body class="signin">
    <div class="signinpanel">
        <div class="row">
            <div class="col-sm-7">
                <div class="signin-info">
                    <div class="logopanel">
                        <div class="logo-icon">
                            <i class="fa fa-heartbeat"></i>
                        </div>
                        <h2 class="system-title">体质测试管理系统</h2>
                    </div>

                    <h4 class="welcome-text">欢迎使用 <strong>大学生体质测试管理系统</strong></h4>

                    <ul class="feature-list">
                        <li><i class="fa fa-database"></i> 体质测试数据管理</li>
                        <li><i class="fa fa-user-md"></i> 学生健康档案</li>
                        <li><i class="fa fa-bar-chart"></i> 测试成绩统计</li>
                        <li><i class="fa fa-file-text-o"></i> 数据分析报告</li>
                        <li><i class="fa fa-users"></i> 多角色权限管理</li>
                        <li><i class="fa fa-mobile"></i> 移动端支持</li>
                    </ul>

                    <div class="register-link" th:if="${isAllowRegister}">
                        <strong>还没有账号？ <a th:href="@{/register}">申请账号 &raquo;</a></strong>
                    </div>
                </div>
            </div>
            <div class="col-sm-5">
                <div class="login-form">
                    <form id="signupForm" autocomplete="off">
                        <h2 class="login-title">系统登录</h2>
                        <p class="login-subtitle">健康体魄，从测试开始</p>

                        <div class="form-group">
                            <input type="text" name="username" class="form-control" placeholder="请输入用户名" value="admin" />
                        </div>

                        <div class="form-group">
                            <input type="password" name="password" class="form-control" placeholder="请输入密码" value="admin123" />
                        </div>

                        <div class="form-group captcha-container" th:if="${captchaEnabled==true}">
                            <div class="captcha-input">
                                <input type="text" name="validateCode" class="form-control" placeholder="验证码" maxlength="5" />
                            </div>
                            <div>
                                <a href="javascript:void(0);" title="点击更换验证码">
                                    <img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="captcha-image" width="120" height="48"/>
                                </a>
                            </div>
                        </div>

                        <div class="checkbox-custom" th:if="${isRemembered}">
                            <input type="checkbox" id="rememberme" name="rememberme">
                            <label for="rememberme">记住我</label>
                        </div>

                        <button class="btn btn-login btn-block" id="btnSubmit" data-loading="正在验证登录，请稍候...">
                            <i class="fa fa-sign-in" style="margin-right: 8px;"></i>立即登录
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <div class="signup-footer">
            <div>
                <p style="margin: 0;">
                    <i class="fa fa-copyright"></i> 2025 大学生体质测试管理系统 All Rights Reserved.
                </p>
                <p style="margin: 5px 0 0 0; opacity: 0.8;">
                    <i class="fa fa-heart" style="color: #e74c3c;"></i> 致力于提升大学生体质健康水平
                </p>
            </div>
        </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.1}"></script>
<script src="../static/ruoyi/login.js" th:src="@{/ruoyi/login.js}"></script>
<script>
$(document).ready(function() {
    // 页面加载完成后的动画效果
    setTimeout(function() {
        $('.signinpanel').addClass('animate__animated animate__fadeIn');
    }, 100);

    // 输入框聚焦效果
    $('.form-control').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        if (!$(this).val()) {
            $(this).parent().removeClass('focused');
        }
    });

    // 验证码点击刷新效果
    $('.captcha-image').on('click', function() {
        $(this).addClass('animate__animated animate__rotateIn');
        setTimeout(() => {
            $(this).removeClass('animate__animated animate__rotateIn');
        }, 600);
    });

    // 登录按钮点击效果
    $('#btnSubmit').on('click', function() {
        var $btn = $(this);
        if (!$btn.hasClass('disabled')) {
            $btn.html('<span class="loading-spinner"></span>正在登录...');
        }
    });

    // 表单验证增强
    $('#signupForm').on('submit', function(e) {
        var username = $('input[name="username"]').val();
        var password = $('input[name="password"]').val();

        // 清除之前的验证状态
        $('.form-control').removeClass('is-valid is-invalid');

        var isValid = true;

        if (!username || username.length < 2) {
            $('input[name="username"]').addClass('is-invalid');
            isValid = false;
        } else {
            $('input[name="username"]').addClass('is-valid');
        }

        if (!password || password.length < 6) {
            $('input[name="password"]').addClass('is-invalid');
            isValid = false;
        } else {
            $('input[name="password"]').addClass('is-valid');
        }

        if (!isValid) {
            e.preventDefault();
            $('#btnSubmit').html('<i class="fa fa-sign-in" style="margin-right: 8px;"></i>立即登录');

            // 震动效果
            $('.login-form').addClass('animate__animated animate__shakeX');
            setTimeout(() => {
                $('.login-form').removeClass('animate__animated animate__shakeX');
            }, 600);
        }
    });

    // 背景粒子效果（简化版）
    function createParticle() {
        const particle = $('<div class="particle"></div>');
        particle.css({
            position: 'absolute',
            width: Math.random() * 4 + 2 + 'px',
            height: Math.random() * 4 + 2 + 'px',
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '50%',
            left: Math.random() * 100 + '%',
            top: '100%',
            pointerEvents: 'none',
            zIndex: 1
        });

        $('body').append(particle);

        particle.animate({
            top: '-10px',
            opacity: 0
        }, Math.random() * 3000 + 2000, function() {
            particle.remove();
        });
    }

    // 每隔一段时间创建粒子
    setInterval(createParticle, 500);

    // 键盘快捷键支持
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            $('#btnSubmit').click();
        }
    });

    // 响应式处理
    function handleResize() {
        if ($(window).width() < 768) {
            $('.signin-info').addClass('mobile-view');
        } else {
            $('.signin-info').removeClass('mobile-view');
        }
    }

    $(window).on('resize', handleResize);
    handleResize();
});
</script>
</body>
</html>
