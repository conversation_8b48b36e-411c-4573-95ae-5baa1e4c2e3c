<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>注册 - 大学生体质测试管理系统</title>
    <meta name="description" content="大学生体质测试管理系统 - 学生账号注册">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.8.1}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">label.error { position:inherit;  }</style>
</head>
<body class="signin">
    <div class="signinpanel">
        <div class="row">
            <div class="col-sm-7">
                <div class="signin-info" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px;">
                    <div class="logopanel m-b">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <i class="fa fa-user-plus" style="font-size: 48px; color: #ffffff; margin-bottom: 15px;"></i>
                            <h2 style="color: #ffffff; margin: 0; font-weight: 300;">账号注册</h2>
                            <p style="color: rgba(255,255,255,0.8); font-size: 14px; margin-top: 5px;">Student Account Registration</p>
                        </div>
                    </div>
                    <div class="m-b"></div>

                    <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                        <h5 style="color: #ffffff; margin-bottom: 15px;">📝 注册须知</h5>
                        <ul class="m-b" style="list-style: none; padding: 0; color: rgba(255,255,255,0.9);">
                            <li style="padding: 5px 0; font-size: 14px;"><i class="fa fa-check m-r-xs"></i> 用户名请使用学号或职员工号</li>
                            <li style="padding: 5px 0; font-size: 14px;"><i class="fa fa-check m-r-xs"></i> 密码长度6-20位，建议包含字母数字</li>
                            <li style="padding: 5px 0; font-size: 14px;"><i class="fa fa-check m-r-xs"></i> 邮箱用于接收体测通知和报告</li>
                            <li style="padding: 5px 0; font-size: 14px;"><i class="fa fa-check m-r-xs"></i> 注册后需等待管理员审核激活</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                        <h6 style="color: #ffffff; margin-bottom: 10px;">🎯 适用人群</h6>
                        <div style="color: rgba(255,255,255,0.9); font-size: 13px;">
                            <p style="margin: 5px 0;">• 在校大学生（本科生、研究生）</p>
                            <p style="margin: 5px 0;">• 体育教师和管理人员</p>
                            <p style="margin: 5px 0;">• 体测数据录入员</p>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <strong style="color: rgba(255,255,255,0.9);">已经注册过? <a th:href="@{/login}" style="color: #ffffff; text-decoration: underline;">直接登录 &raquo;</a></strong>
                    </div>
                </div>
            </div>
            <div class="col-sm-5">
                <form id="registerForm" autocomplete="off" style="padding: 40px 30px;">
                    <h3 style="text-align: center; color: #2c3e50; margin-bottom: 10px; font-weight: 300;">创建账号</h3>
                    <p style="text-align: center; color: #7f8c8d; margin-bottom: 30px; font-size: 14px;">请填写完整信息进行注册</p>

                    <div style="margin-bottom: 15px;">
                        <input type="text" name="username" class="form-control uname" placeholder="👤 学号/工号（作为用户名）" maxlength="20"
                               style="height: 45px; padding: 12px 15px; border-radius: 6px;" />
                        <small style="color: #95a5a6; font-size: 12px;">请输入您的学号或职员工号</small>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <input type="email" name="email" class="form-control" placeholder="📧 邮箱地址" maxlength="50"
                               style="height: 45px; padding: 12px 15px; border-radius: 6px;" />
                        <small style="color: #95a5a6; font-size: 12px;">用于接收体测通知和报告</small>
                    </div>

                    <input type="password" name="password" class="form-control pword" placeholder="🔒 设置密码" maxlength="20"
                           style="margin-bottom: 15px; height: 45px; padding: 12px 15px; border-radius: 6px;" />
                    <input type="password" name="confirmPassword" class="form-control pword" placeholder="🔒 确认密码" maxlength="20"
                           style="margin-bottom: 15px; height: 45px; padding: 12px 15px; border-radius: 6px;" />

					<div class="row m-t" th:if="${captchaEnabled==true}" style="margin-bottom: 15px;">
						<div class="col-xs-6">
						    <input type="text" name="validateCode" class="form-control code" placeholder="🔢 验证码" maxlength="5"
						           style="height: 45px; padding: 12px 15px; border-radius: 6px;" />
						</div>
						<div class="col-xs-6">
							<a href="javascript:void(0);" title="点击更换验证码">
								<img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="imgcode" width="85%"
								     style="height: 45px; border-radius: 6px; border: 1px solid #ddd;"/>
							</a>
						</div>
					</div>

                    <div class="checkbox-custom" th:classappend="${captchaEnabled==false} ? 'm-t'" style="margin-bottom: 20px;">
				        <input type="checkbox" id="acceptTerm" name="acceptTerm">
				        <label for="acceptTerm" style="color: #7f8c8d; font-size: 14px;">我已阅读并同意
				            <a href="#" style="color: #667eea;">用户协议</a> 和 <a href="#" style="color: #667eea;">隐私政策</a>
				        </label>
				    </div>

                    <button class="btn btn-success btn-block" id="btnSubmit" data-loading="正在验证注册，请稍候..."
                            style="height: 45px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; font-size: 16px; margin-bottom: 20px;">
                        ✨ 立即注册
                    </button>

                    <div style="text-align: center; padding-top: 15px; border-top: 1px solid #eee;">
                        <p style="color: #95a5a6; font-size: 12px; margin: 0;">
                            📢 注册后需要管理员审核激活<br>
                            请耐心等待，我们会尽快处理您的申请
                        </p>
                    </div>
                </form>
            </div>
        </div>
        <div class="signup-footer">
            <div class="pull-left">
                &copy; 2025 大学生体质测试管理系统 All Rights Reserved. <br>
                <small style="opacity: 0.8;">致力于提升大学生体质健康水平 • 科学测试 数据驱动</small>
            </div>
        </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.extend.js" th:src="@{/ajax/libs/validate/jquery.validate.extend.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.1}"></script>
<script src="../static/ruoyi/register.js" th:src="@{/ruoyi/register.js}"></script>
</body>
</html>
