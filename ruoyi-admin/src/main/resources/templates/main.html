<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>大学生体质测试管理系统</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
</head>

<body class="gray-bg">
    <div class="row border-bottom white-bg dashboard-header">
        <div class="col-sm-3">
            <h2>欢迎使用</h2>
            <small>大学生体质测试管理系统</small>
            <br>
            <br>
            <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white;">
                <i class="fa fa-heartbeat" style="font-size: 48px; margin-bottom: 10px;"></i>
                <h4 style="margin: 0; color: white;">体质健康</h4>
                <small style="color: rgba(255,255,255,0.9);">科学测试 数据驱动</small>
            </div>
            <br>
        </div>
        <div class="col-sm-5">
            <h2>大学生体质测试管理系统</h2>
            <p>本系统是专门为高等院校设计的<b>体质健康测试管理平台</b>，严格按照《国家学生体质健康标准》要求开发。系统集成了<b>学生信息管理</b>、<b>体测数据录入</b>、<b>成绩统计分析</b>、<b>报告生成</b>等核心功能，为学校体育部门提供全方位的体质测试管理解决方案。通过科学的数据分析，帮助学校更好地了解学生体质健康状况，制定针对性的体育教学计划。</p>
            <p>
                <b>系统版本：</b><span>v[[${version}]]</span>
            </p>
            <p>
                <span class="label label-primary">🏃‍♂️ 体质健康</span>
                <span class="label label-success">📊 数据管理</span>
                <span class="label label-info">📈 统计分析</span>
            </p>
            <br>
            <p>
                <a class="btn btn-primary btn-outline" href="javascript:void(0);" onclick="showTestItems()">
                    <i class="fa fa-list-alt"> </i> 测试项目
                </a>
                <a class="btn btn-success btn-outline" href="javascript:void(0);" onclick="showSystemFeatures()">
                    <i class="fa fa-cogs"></i> 系统功能
                </a>
            </p>
        </div>
        <div class="col-sm-4">
            <h4>🎯 测试项目（国标）：</h4>
            <ol>
                <li><i class="fa fa-user"></i> 身高体重（BMI指数）</li>
                <li><i class="fa fa-heart"></i> 肺活量测试</li>
                <li><i class="fa fa-flash"></i> 50米跑（速度）</li>
                <li><i class="fa fa-arrow-up"></i> 立定跳远（爆发力）</li>
                <li><i class="fa fa-expand"></i> 坐位体前屈（柔韧性）</li>
                <li><i class="fa fa-male"></i> 仰卧起坐（女）/ 引体向上（男）</li>
                <li><i class="fa fa-clock-o"></i> 800米跑（女）/ 1000米跑（男）</li>
                <li><i class="fa fa-plus"></i> 其他选测项目</li>
            </ol>
        </div>

    </div>
    <div class="wrapper wrapper-content">
        <div class="row">
            <div class="col-sm-4">

                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>📞 系统信息</h5>

                    </div>
                    <div class="ibox-content">
                        <p><i class="fa fa-university"></i> 适用对象：高等院校体育部门
                        </p>
                        <p><i class="fa fa-qq"></i> QQ群：<s>满1389287</s> <s>满1679294</s> <s>满1529866</s> <s>满1772718</s> <s>满1366522</s> <s>满1382251</s> <s>满1145125</s> <s>满86752435</s> <s>满134072510</s> <s>满210336300</s> <s>满339522636</s> <s>满130035985</s> <s>满143151071</s> <s>满158781320</s> <s>满201531282</s> <s>满101526938</s> <s>满264355400</s> <s>满298522656</s> <s>满139845794</s>  <s>满185760789</s> <s>满175104288</s> <s>满174942938</s> <s>满287843737</s> <s>满232896766</s> <s>满180208928</s> <a href="http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=WqsGDxpGkqOPeWGOf3I32f_rXxdhqYNr&authKey=kvdF5df7PO9bzWxmixKhZN6ShsECBiuGUmmzTZBWVr2MVOfJ8%2F4oD0Gws0rbgYfz&noverify=0&group_code=140284548" target="_blank">140284548</a>
                        </p>
                        <p><i class="fa fa-weixin"></i> 微信：<a href="javascript:;">/ *若依</a>
                        </p>
                        <p><i class="fa fa-credit-card"></i> 支付宝：<a href="javascript:;" class="支付宝信息">/ *若依</a>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>更新日志</h5>
                    </div>
                    <div class="ibox-content no-padding">
                        <div class="panel-body">
                            <div class="panel-group" id="version">
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v481">v4.8.1</a><code class="pull-right">2025.05.20</code>
								   </h5>
								</div>
								<div id="v481" class="panel-collapse collapse in">
									<div class="panel-body">
									   <ol>
									        <li>新增CSRF防护功能</li>
									        <li>Excel导入导出支持多图片</li>
									        <li>菜单管理支持批量保存排序</li>
									        <li>用户分配角色页禁用不允许分配</li>
									        <li>优化Tab页签跟随主题样式效果</li>
									        <li>优化浅色主题下菜单右边框同步主题色</li>
									        <li>优化空指针异常时日志无法记录错误信息问题</li>
									        <li>新增表格参数（自定义radio/checkbox的name值）</li>
									        <li>升级oshi到最新版本6.8.1</li>
									        <li>升级tomcat到最新版本9.0.105</li>
									        <li>升级commons.io到最新版本2.19.0</li>
									        <li>升级bootstrap-table到最新版本1.24.1</li>
									        <li>升级bootstrap-fileinput到最新版本5.5.4</li>
									        <li>用户更新方法移除login_name更新字段</li>
									        <li>修复定时任务参数值带括号时异常问题</li>
									        <li>进入新增页面前方法校验数据权限</li>
									        <li>进入授权角色&重置密码页校验数据权限</li>
									        <li>优化Excel匹配数值型.0结尾</li>
									        <li>优化HttpUtils加入请求类型传参</li>
									        <li>优化管理员登录不设置权限permissions属性</li>
									        <li>优化successCallback方法对于非特定表格类型无响应问题</li>
									        <li>优化导出Excel日期格式双击离开后与设定的格式不一致问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v480">v4.8.0</a><code class="pull-right">2024.12.26</code>
								   </h5>
								</div>
								<div id="v480" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>支持自定义显示Excel属性列</li>
									        <li>表格默认转义HTML字符串</li>
									        <li>新增列宽拖动长内容自适应显示示例</li>
									        <li>Excel注解支持wrapText是否允许内容换行</li>
									        <li>代码生成新增配置是否允许文件覆盖到本地</li>
									        <li>升级oshi到最新版本6.6.5</li>
									        <li>升级tomcat到最新版本9.0.96</li>
									        <li>升级logback到最新版本1.2.13</li>
									        <li>升级commons.io到最新版本2.16.1</li>
									        <li>升级spring-framework到最新版本5.3.39</li>
									        <li>升级jquery.validate到最新版本v1.21.0</li>
									        <li>优化导入带标题文件关闭清理</li>
									        <li>代码生成创建表屏蔽违规的字符</li>
									        <li>修复主子表数据显示问题</li>
									        <li>修复记住我请求头过大的问题</li>
									        <li>修复角色禁用权限不失效问题</li>
									        <li>修复类匿名注解访问失效问题</li>
									        <li>修复导出子列表对象只能在最后的问题</li>
									        <li>修复多选下拉框open导致页签空白问题</li>
									        <li>优化身份证脱敏正则</li>
									        <li>优化查询时间范围日期格式</li>
									        <li>优化异步树表格折叠同步子状态</li>
									        <li>优化时间控件清除按钮样式问题</li>
									        <li>优化表格图片预览动态路径显示问题</li>
									        <li>优化select2下拉框必填背景色无法清空问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v479">v4.7.9</a><code class="pull-right">2024.06.06</code>
								   </h5>
								</div>
								<div id="v479" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>通知公告新增详细显示</li>
									        <li>新增数据脱敏过滤注解</li>
									        <li>新增表格示例（虚拟滚动）</li>
									        <li>新增表格示例（全文检索）</li>
									        <li>新增表格示例（保存状态）</li>
									        <li>代码生成支持表单布局选项</li>
									        <li>限制用户操作数据权限范围</li>
									        <li>用户密码新增非法字符验证</li>
									        <li>默认加载layer扩展皮肤</li>
									        <li>未修改初始密码弹框提醒</li>
									        <li>定时任务白名单配置范围缩小</li>
									        <li>操作日志列表重置回第一页</li>
									        <li>定时任务日志默认按时间排序</li>
									        <li>Excel注解ColumnType类型新增文本</li>
									        <li>Excel注解新增属性comboReadDict</li>
									        <li>新增Anonymous匿名访问不鉴权注解</li>
									        <li>升级oshi到最新版本6.6.1</li>
									        <li>升级druid到最新版本1.2.23</li>
									        <li>升级commons.io到最新版本2.13.0</li>
									        <li>升级spring-framework到安全版本</li>
									        <li>升级bootstrap-table到最新版本1.22.6</li>
									        <li>修复重置日期时出现的异常问题</li>
									        <li>修复页签关闭后存在的跳转问题</li>
									        <li>修复tooltip单击复制文本不生效的问题</li>
									        <li>更新缓存管理键名排序方式</li>
									        <li>更新HttpUtils中的User-Agent</li>
									        <li>优化自定义XSS注解匹配方式</li>
									        <li>优化登录注册页面验证码验证</li>
									        <li>优化数据权限自定义匹配方式</li>
									        <li>优化高频率定时任务不执行问题</li>
									        <li>优化树表格align属性在标题生效</li>
									        <li>优化代码生成主子表关联查询方式</li>
									        <li>优化导入Excel时设置dictType属性重复查缓存问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v478">v4.7.8</a><code class="pull-right">2023.11.23</code>
								   </h5>
								</div>
								<div id="v478" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>用户列表新增抽屉效果详细信息</li>
									        <li>操作日志列表新增IP地址查询</li>
									        <li>定时任务新增页去除状态选项</li>
									        <li>系统管理角色列表显示数据权限</li>
									        <li>通用排序属性orderBy参数限制长度</li>
									        <li>新增isScrollToTop页签切换滚动到顶部</li>
									        <li>Excel自定义数据处理器增加单元格/工作簿对象</li>
									        <li>新增表格参数（数据值为空时显示的内容undefinedText）</li>
									        <li>升级oshi到最新版本6.4.7</li>
									        <li>升级shiro到最新版本1.13.0</li>
									        <li>升级druid到最新版本1.2.20</li>
									        <li>升级pagehelper到最新版1.4.7</li>
									        <li>升级spring-boot到最新版本2.5.15</li>
									        <li>升级jquery到最新版v3.7.1</li>
									        <li>升级layer到最新版本v3.7.0</li>
									        <li>升级layui到最新版本v2.8.18</li>
									        <li>升级x-editable到最新版本1.5.3</li>
									        <li>修复自定义字典样式不生效的问题</li>
									        <li>修复弹窗按钮启用禁用方法无效问题</li>
									        <li>修复横向菜单关闭最后一个页签状态问题</li>
									        <li>修复Excel导入数据临时文件无法删除问题</li>
									        <li>修复表格行内编辑启用翻页记住选择无效问题</li>
									        <li>修复Excels导入时无法获取到dictType字典值问题</li>
									        <li>优化重置密码鼠标按下显示密码</li>
									        <li>优化参数键值文本框改为文本域</li>
									        <li>优化表格重置默认返回到第一页</li>
									        <li>优化菜单管理类型为按钮状态可选</li>
									        <li>优化数字金额大写转换精度丢失问题</li>
									        <li>优化树表查询无数据时清除分页信息</li>
									        <li>优化通用detail详细信息弹窗不显示按钮</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v477">v4.7.7</a><code class="pull-right">2023.04.14</code>
								   </h5>
								</div>
								<div id="v477" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>操作日志新增消耗时间属性</li>
									        <li>日志管理使用索引提升查询性能</li>
									        <li>日志注解支持排除指定的请求参数</li>
									        <li>新增监控页面图标显示</li>
									        <li>新增支持登录IP黑名单限制</li>
									        <li>更新fontawesome图标示例</li>
									        <li>屏蔽定时任务bean违规的字符</li>
									        <li>支持自定义隐藏属性列过滤子对象</li>
									        <li>连接池Druid支持新的配置connectTimeout和socketTimeout</li>
									        <li>升级jquery到最新版v3.6.3</li>
									        <li>升级layui到最新版本2.7.6</li>
									        <li>升级jasny-bootstrap到最新版4.0.0</li>
									        <li>升级oshi到最新版本6.4.1</li>
									        <li>升级druid到最新版本1.2.16</li>
									        <li>修复异步表格树子项排序问题</li>
									        <li>修复冻结列不支持IE浏览器的问题</li>
									        <li>修复主子表使用suggest插件无法新增问题</li>
									        <li>修复菜单栏快速点击导致展开折叠样式问题</li>
									        <li>修复用户多角色数据权限可能出现权限抬升的情况</li>
									        <li>修复异步加载表格树重置列表父节点展开异常问题</li>
									        <li>修复页签属性refresh为undefined时页面被刷新问题</li>
									        <li>移除apache/commons-fileupload依赖</li>
									        <li>优化前端属性提醒说明</li>
									        <li>优化用户导入更新时需获取用户编号问题</li>
									        <li>优化主子表根据序号删除方法加入表格ID参数</li>
									        <li>优化导出Excel时设置dictType属性重复查缓存问题</li>
									        <li>优化在线用户服务缓存改为从Bean容器获取不使用自动装配</li>
									        <li>优化表格示例行拖拽后列表底部总记录条数变成了undefined问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v476">v4.7.6</a><code class="pull-right">2022.12.16</code>
								   </h5>
								</div>
								<div id="v476" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>定时任务违规的字符</li>
									        <li>忽略不必要的属性数据返回</li>
									        <li>导入更新用户数据前校验数据权限</li>
									        <li>修改参数键名时移除前缓存配置</li>
									        <li>修改用户登录账号进行重复验证</li>
									        <li>兼容Excel下拉框内容过多无法显示</li>
									        <li>升级oshi到最新版本6.4.0</li>
									        <li>升级kaptcha到最新版2.3.3</li>
									        <li>升级druid到最新版本1.2.15</li>
									        <li>升级shiro到最新版本1.10.1</li>
									        <li>升级pagehelper到最新版1.4.6</li>
									        <li>升级bootstrap-fileinput到最新版本5.5.2</li>
									        <li>修复sheet超出最大行数异常问题</li>
									        <li>修复关闭父页签后提交无法跳转的问题</li>
									        <li>修复操作日志类型多选导出不生效问题</li>
									        <li>修复导出包含空子列表数据异常的问题</li>
									        <li>优化树形表格层级显示</li>
									        <li>优化SQL关键字检查防止注入</li>
									        <li>优化用户管理重置时取消部门选择</li>
									        <li>优化代码生成同步后字典值NULL问题</li>
									        <li>优化导出对象的子列表为空会出现[]问题</li>
									        <li>优化select2搜索下拉后校验必填样式问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v475">v4.7.5</a><code class="pull-right">2022.09.05</code>
								   </h5>
								</div>
								<div id="v475" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>Excel支持导出对象的子列表方法</li>
									        <li>数据逻辑删除不进行唯一验证</li>
									        <li>优化多角色数据权限匹配规则</li>
									        <li>新增主子表提交校验示例</li>
									        <li>支持自定义隐藏Excel属性列</li>
									        <li>Excel注解支持backgroundColor属性设置背景颜色</li>
									        <li>菜单配置刷新时Tab页签切换时刷新</li>
									        <li>增加对AjaxResult消息结果类型的判断</li>
									        <li>新增示例（进度条）</li>
									        <li>新增内容编码/解码方便插件集成使用</li>
									        <li>升级jquery到最新版3.6.1</li>
									        <li>升级layui到最新版本2.7.5</li>
									        <li>升级shiro到最新版本1.9.1</li>
									        <li>升级druid到最新版本1.2.11</li>
									        <li>升级pagehelper到最新版1.4.3</li>
									        <li>升级oshi到最新版本6.2.2</li>
									        <li>修复树表onLoadSuccess不生效的问题</li>
											<li>修复用户分配角色大于默认页数丢失问题</li>
									        <li>定时任务支持执行父类方法</li>
									        <li>自动设置切换多个树表格实例配置</li>
									        <li>页签创建标题优先data-title属性</li>
									        <li>优化任务过期不执行调度</li>
									        <li>优化横向菜单下激活菜单样式</li>
									        <li>优化按钮打开窗口后按回车反复弹出</li>
									        <li>优化excel/scale属性导出单元格数值类型</li>
									        <li>优化druid开启wall过滤器出现的异常问题</li>
									        <li>优化多个相同角色数据导致权限SQL重复问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v474">v4.7.4</a><code class="pull-right">2022.06.01</code>
								   </h5>
								</div>
								<div id="v474" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>用户头像上传图片格式限制</li>
									        <li>Excel注解支持color属性设置字体颜色</li>
									        <li>设置分页参数默认值</li>
									        <li>主子表操作列新增单个删除</li>
									        <li>定时任务检查Bean包名是否为白名单配置</li>
									        <li>升级spring-boot到最新版本2.5.14</li>
									        <li>升级shiro到最新版本1.9.0</li>
									        <li>升级oshi到最新版本6.1.6</li>
									        <li>升级fastjson到最新版1.2.83 安全修复版本</li>
									        <li>文件上传兼容Weblogic环境</li>
									        <li>新增清理分页的线程变量方法</li>
									        <li>新增获取不带后缀文件名称方法</li>
									        <li>用户缓存信息添加部门ancestors祖级列表</li>
									        <li>自定义ShiroFilterFactoryBean防止中文请求被拦截</li>
									        <li>字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）</li>
									        <li>优化IP地址获取到多个的问题</li>
									        <li>优化表格冻结列阴影效果显示</li>
									        <li>优化菜单侧边栏滚动条尺寸及颜色</li>
									        <li>优化显示顺序orderNum类型为整型</li>
									        <li>优化接口使用泛型使其看到响应属性字段</li>
									        <li>优化导出数据LocalDateTime类型无数据问题</li>
									        <li>修复导入Excel时字典字段类型为Long转义为空问题</li>
									        <li>优化导出excel单元格验证,包含变更为开头.防止正常内容被替换</li>
									        <li>修复URL类型回退键被禁止问题</li>
									        <li>修复表格客户端分页序号显示错误问题</li>
									        <li>修复代码生成拖拽多次出现的排序不正确问题</li>
									        <li>修复表格打印组件不识别多层对象属性值问题</li>
									        <li>修复操作日志查询类型条件为0时会查到所有数据</li>
									        <li>修复Excel注解prompt/combo同时使用不生效问题</li>
									        <li>修复初始化多表格处理回调函数时获取的表格配置不一致问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v473">v4.7.3</a><code class="pull-right">2022.03.01</code>
								   </h5>
								</div>
								<div id="v473" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>表格树支持分页/异步加载</li>
									        <li>代码生成预览支持复制内容</li>
									        <li>定时任务默认保存到内存中执行</li>
									        <li>代码生成同步保留必填/类型选项</li>
									        <li>页面若未匹配到字典标签则返回原字典值</li>
									        <li>用户访问控制时校验数据权限，防止越权</li>
									        <li>导出Excel时屏蔽公式，防止CSV注入风险</li>
									        <li>升级spring-boot到最新版本2.5.10</li>
									        <li>升级spring-boot-mybatis到最新版2.2.2</li>
									        <li>升级pagehelper到最新版1.4.1</li>
									        <li>升级oshi到最新版本6.1.2</li>
									        <li>升级bootstrap-table到最新版本1.19.1</li>
									        <li>服务监控新增运行参数信息显示</li>
									        <li>定时任务目标字符串验证包名白名单</li>
									        <li>文件上传接口新增原/新文件名返回参数</li>
									        <li>定时任务屏蔽违规的字符</li>
									        <li>分页数据新增分页参数合理化参数</li>
									        <li>表格父子视图添加点击事件打开示例</li>
									        <li>优化上传文件名称命名规则</li>
									        <li>优化加载字典缓存数据</li>
									        <li>优化任务队列满时任务拒绝策略</li>
									        <li>优化IE11上传预览不显示的问题</li>
									        <li>优化Excel格式化不同类型的日期对象</li>
									        <li>优化国际化配置多余的zh请求问题</li>
									        <li>优化新版Chrome浏览器回退出现的遮罩层</li>
									        <li>修复EMAIL类型回退键被禁止问题</li>
									        <li>修复Xss注解字段值为空时的异常问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v472">v4.7.2</a><code class="pull-right">2021.12.23</code>
								   </h5>
								</div>
								<div id="v472" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>自定义xss校验注解实现</li>
									        <li>进入修改页面方法添加权限标识</li>
									        <li>代码生成创建按钮添加超级管理员权限</li>
									        <li>代码生成创建表检查关键字，防止注入风险</li>
									        <li>修复定时任务多参数逗号分隔的问题</li>
									        <li>修复表格插件一起使用出现的声明报错问题</li>
									        <li>修复代码生成主子表模板删除方法缺少事务</li>
									        <li>升级oshi到最新版本v5.8.6</li>
									        <li>升级velocity到最新版本2.3</li>
									        <li>升级fastjson到最新版1.2.79</li>
									        <li>升级log4j2到最新版2.17.0 防止漏洞风险</li>
									        <li>升级thymeleaf到最新版3.0.14 阻止远程代码执行漏洞</li>
									        <li>优化修改/授权角色实时生效</li>
									        <li>修整tomcat配置参数已过期问题</li>
									        <li>前端添加单独的二代身份证校验</li>
									        <li>优化新增部门时验证用户所属部门</li>
									        <li>优化查询用户的角色组&岗位组代码</li>
									        <li>请求分页方法设置成通用方便灵活调用</li>
									        <li>优化日期类型错误提示与图标重叠问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v471">v4.7.1</a><code class="pull-right">2021.11.10</code>
								   </h5>
								</div>
								<div id="v471" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>新增是否开启页签功能</li>
									        <li>代码生成的模块增加创建表功能</li>
									        <li>Excel导入支持@Excels注解</li>
									        <li>Excel注解支持导入导出标题信息</li>
									        <li>Excel注解支持自定义数据处理器</li>
									        <li>日志注解新增是否保存响应参数</li>
									        <li>防重提交注解支持配置间隔时间/提示消息</li>
									        <li>网页部分操作禁止使用后退键（Backspace）</li>
									        <li>实例演示中增加多层窗口获取值</li>
									        <li>弹出层openOptions增加动画属性</li>
									        <li>升级spring-boot到最新版本2.5.6</li>
									        <li>升级spring-boot-mybatis到最新版2.2.0</li>
									        <li>升级pagehelper到最新版1.4.0</li>
									        <li>升级oshi到最新版本v5.8.2</li>
									        <li>升级druid到最新版1.2.8</li>
									        <li>升级fastjson到最新版1.2.78</li>
									        <li>升级thymeleaf-extras-shiro到最新版本v2.1.0</li>
									        <li>升级bootstrap-fileinput到最新版本v5.2.4</li>
									        <li>修改阿里云maven仓库地址为新版地址</li>
									        <li>定时任务屏蔽违规字符</li>
									        <li>增加sendGet无参请求方法</li>
									        <li>代码生成去掉多余的排序字段</li>
									        <li>优化启动脚本参数优化</li>
									        <li>优化页签关闭右侧清除iframe元素</li>
									        <li>优化多表格切换表单查询参数</li>
									        <li>优化表格实例切换event不能为空</li>
									        <li>优化mybatis全局默认的执行器</li>
									        <li>优化导入Excel数据关闭时清理file</li>
									        <li>优化Excel导入图片可能出现的异常</li>
									        <li>优化记录登录信息，防止不必要的修改</li>
									        <li>优化aop语法，使用spring自动注入注解</li>
									        <li>修复无法被反转义问题</li>
									        <li>修复拖拽行数据错位问题</li>
									        <li>修复新窗口打开页面关闭弹窗报错</li>
									        <li>修复富文本回退键被禁止&控制台报错问题</li>
									        <li>修复自定义弹出层全屏参数无效问题</li>
									        <li>修复树表代码生成短字段无法识别问题</li>
									        <li>修复apple/webkit浏览器时间无法格式化</li>
									        <li>修复后端主子表代码模板方法名生成错误问题</li>
									        <li>修复swagger没有指定dataTypeClass导致启动出现warn日志</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v470">v4.7.0</a><code class="pull-right">2021.09.01</code>
								   </h5>
								</div>
								<div id="v470" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>优化弹出层显示在顶层窗口</li>
									        <li>定时任务支持在线生成cron表达式</li>
									        <li>Excel注解支持Image图片导入</li>
									        <li>支持配置是否开启记住我功能</li>
									        <li>修改时检查用户数据权限范围</li>
									        <li>表单重置开始/结束时间控件</li>
									        <li>新增多图上传示例</li>
									        <li>启用父部门状态排除顶级节点</li>
									        <li>富文本默认dialogsInBody属性</li>
									        <li>去除默认分页合理化参数</li>
									        <li>顶部菜单跳转添加绝对路径</li>
									        <li>升级oshi到最新版本v5.8.0</li>
									        <li>升级shiro到最新版本v1.8.0</li>
									        <li>升级commons.io到最新版本v2.11.0</li>
									        <li>升级jquery到最新版v3.6.0</li>
									        <li>升级icheck到最新版v1.0.3</li>
									        <li>升级layer到最新版本v3.5.1</li>
									        <li>升级layui到最新版本v2.6.8</li>
									        <li>升级laydate到最新版本v5.3.1</li>
									        <li>升级select2到最新版v4.0.13</li>
									        <li>升级cropper到最新版本v1.5.12</li>
									        <li>升级summernote到最新版本v0.8.18</li>
									        <li>升级duallistbox到最新版本v3.0.9</li>
									        <li>升级jquery.validate到最新版本v1.19.3</li>
									        <li>升级bootstrap-suggest到最新版本v0.1.29</li>
									        <li>升级bootstrap-select到最新版本v1.13.18</li>
									        <li>升级bootstrap-fileinput到最新版本v5.2.3</li>
									        <li>查询表格指定列值增加是否去重属性</li>
									        <li>删除sourceMappingURL源映射</li>
									        <li>去除多余的favicon.ico引入</li>
									        <li>优化代码生成模板</li>
									        <li>优化XSS跨站脚本过滤</li>
									        <li>补充定时任务表字段注释</li>
									        <li>定时任务屏蔽ldap远程调用</li>
									        <li>定时任务屏蔽http(s)远程调用</li>
									        <li>定时任务对检查异常进行事务回滚</li>
									        <li>调度日志详细页添加关闭按钮</li>
									        <li>优化异常打印输出信息</li>
									        <li>优化移动端进入首页样式</li>
									        <li>优化用户操作不能删除自己</li>
									        <li>默认开始/结束时间绑定控件选择类型</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v462">v4.6.2</a><code class="pull-right">2021.07.01</code>
								   </h5>
								</div>
								<div id="v462" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>优化参数&字典缓存操作</li>
									        <li>新增表格参数（导出方式&导出文件类型）</li>
									        <li>新增表格示例（自定义视图分页）</li>
									        <li>新增示例（表格列拖拽）</li>
									        <li>集成yuicompressor实现(CSS/JS压缩)</li>
									        <li>新增表格参数（是否支持打印页面showPrint）</li>
									        <li>支持bat脚本执行应用</li>
									        <li>修复存在的SQL注入漏洞问题</li>
									        <li>定时任务屏蔽rmi远程调用</li>
									        <li>导出Excel文件支持数据流下载方式</li>
									        <li>实例演示弹层组件增加相册层示例</li>
									        <li>删除操作日志记录信息</li>
									        <li>增加表格重置分页的参数</li>
									        <li>限制超级管理员不允许操作</li>
									        <li>树级结构更新子节点使用replaceFirst</li>
									        <li>支持动态生成密匙，防止默认密钥泄露</li>
									        <li>升级pagehelper到最新版1.3.1</li>
									        <li>升级oshi到最新版本v5.7.4</li>
									        <li>升级swagger到最新版本v3.0.0</li>
									        <li>升级commons.io到最新版本v2.10.0</li>
									        <li>升级commons.fileupload到最新版本v1.4</li>
									        <li>升级bootstrap-table到最新版本v1.18.3</li>
									        <li>升级druid到最新版本v1.2.6</li>
									        <li>升级fastjson到最新版1.2.76</li>
									        <li>升级layui到最新版本v2.6.6</li>
									        <li>升级layer到最新版本v3.5.0</li>
									        <li>升级laydate到最新版本v5.3.0</li>
									        <li>优化表格树移动端&边框显示</li>
									        <li>新增表格刷新options配置方法</li>
									        <li>优化图片工具类读取文件，防止异常</li>
									        <li>修复表格图片预览移动端宽高无效问题</li>
									        <li>主子表通用操作封装处理增加文本域类型</li>
									        <li>日志注解兼容获取json类型的参数</li>
									        <li>修复表单向导插件有滚动条时底部工具栏无法固定问题</li>
									        <li>修复导出角色数据范围翻译缺少仅本人</li>
									        <li>修正Velocity模板初始字符集</li>
									        <li>升级mybatis到最新版3.5.6 阻止远程代码执行漏洞</li>
									        <li>优化代码生成导出模板名称</li>
									        <li>修改个人中心密码长度提醒</li>
									        <li>实例演示中弹出表格增加以回调形式回显到父窗体</li>
									        <li>修复登录页面弹窗文字不显示的问题</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v461">v4.6.1</a><code class="pull-right">2021.04.12</code>
								   </h5>
								</div>
								<div id="v461" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>新增IE浏览器版本过低提示页面</li>
									        <li>新增详细信息tab页签方式</li>
									        <li>新增解锁屏幕打开上次页签</li>
									        <li>数据监控默认账户密码防止越权访问</li>
									        <li>新增表格示例（导出选择列）</li>
									        <li>个人信息添加手机&邮箱重复验证</li>
									        <li>个人中心刷新后样式问题</li>
									        <li>操作日志返回参数添加非空验证</li>
									        <li>velocity剔除commons-collections版本，防止3.2.1版本的反序列化漏洞</li>
									        <li>子表模板默认日期格式化</li>
									        <li>代码生成预览语言根据后缀名高亮显示</li>
									        <li>代码生成主子表相同字段导致数据问题</li>
									        <li>升级SpringBoot到最新版本2.2.13</li>
									        <li>升级shiro到最新版1.7.1 阻止身份认证绕过漏洞</li>
									        <li>升级bootstrapTable到最新版本v1.18.2</li>
									        <li>升级bootstrapTable相关组件到最新版本v1.18.2</li>
									        <li>升级fastjson到最新版1.2.75</li>
									        <li>升级druid到最新版本v1.2.4</li>
									        <li>升级oshi到最新版本v5.6.0</li>
									        <li>修改ip字段长度防止ipv6地址长度不够</li>
									        <li>搜索建议示例选择后隐藏列表</li>
									        <li>主子表示例增加初始化数据</li>
									        <li>优化Excel导入增加空行判断</li>
									        <li>修复横向菜单无法打开页签问题</li>
									        <li>修复导入数据为负浮点数时，导入结果会丢失精度问题</li>
									        <li>优化更多操作按钮左侧移入内容闪现消失情况</li>
									        <li>修复主子表提交中列隐藏后出现列偏移问题</li>
									        <li>单据打印网页时通过hidden-print隐藏元素</li>
									        <li>表格销毁清除记住选择数据</li>
									        <li>增加表格动态列示例</li>
									        <li>代码生成选择主子表关联元素必填</li>
									        <li>tree根据Id和Name选中指定节点增加空判断</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v46">v4.6.0</a><code class="pull-right">2021.01.01</code>
								   </h5>
								</div>
								<div id="v46" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>新增缓存监控管理</li>
									        <li>新增锁定屏幕功能</li>
									        <li>菜单新增是否刷新页面</li>
									        <li>删除用户和角色解绑关联</li>
									        <li>新增密码强度字符范围提示</li>
									        <li>防止匿名访问进行过滤</li>
									        <li>升级SpringBoot到最新版本2.2.12</li>
									        <li>升级poi到最新版本4.1.2</li>
									        <li>升级bitwalker到最新版本1.21</li>
									        <li>升级bootstrap-fileinput到最新版本5.1.3</li>
									        <li>升级bootstrapTable到最新版本v1.18.0</li>
									        <li>升级bootstrapTable相关组件到最新版本v1.18.0</li>
									        <li>升级oshi到最新版本v5.3.6</li>
									        <li>新增示例（标签 & 提示）</li>
									        <li>添加单据打印示例</li>
									        <li>修改表格初始参数sortName默认值为undefined</li>
									        <li>新增表格参数（自定义打印页面模板printPageBuilder）</li>
									        <li>新增表格参数（是否显示行间隔色striped）</li>
									        <li>新增表格参数（渲染完成后执行的事件onPostBody）</li>
									        <li>Excel注解支持Image图片导出</li>
									        <li>Excel支持注解align对齐方式</li>
									        <li>Excel支持导入Boolean型数据</li>
									        <li>主子表操作添加通用addColumn方法</li>
									        <li>代码生成日期控件区分范围</li>
									        <li>代码生成数据库文本类型生成表单文本域</li>
									        <li>修复生成主子表外键名错误</li>
									        <li>选项卡新增是否刷新属性</li>
									        <li>修复树表格表头跟表格宽度不同步的问题</li>
									        <li>表格树加载完成触发tooltip方法</li>
									        <li>使用widthUnit定义树表格选项单位</li>
									        <li>修复主子表editColumn序列问题</li>
									        <li>修复添加全屏在无参数时没有替换url参数问题</li>
									        <li>弹出层openOptions移动端自适应</li>
									        <li>防止错误页返回主页出现嵌套问题</li>
									        <li>设置回显数据字典验证防止空值</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v451">v4.5.1</a><code class="pull-right">2020.11.18</code>
								   </h5>
								</div>
								<div id="v451" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>阻止任意文件下载漏洞</li>
									        <li>升级shiro到最新版1.7.0 阻止权限绕过漏洞</li>
									        <li>升级druid到最新版本v1.2.2</li>
									        <li>新增表格行触发事件（onCheck、onUncheck、onCheckAll、onUncheckAll）</li>
									        <li>修复多页签关闭非当前选项出现空白问题</li>
									        <li>代码生成预览支持高亮显示</li>
									        <li>mapperLocations配置支持分隔符</li>
									        <li>权限信息调整</li>
									        <li>个人中心头像和上传头像增加默认图片</li>
									        <li>全局配置类保持和其他应用命名相同</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v45">v4.5.0</a><code class="pull-right">2020.10.20</code>
								   </h5>
								</div>
								<div id="v45" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>新增菜单导航显示风格（default为左侧导航菜单，topnav为顶部导航菜单）</li>
									        <li>菜单&数据权限新增（展开/折叠 全选/全不选 父子联动）</li>
									        <li>账号密码支持自定义更新周期</li>
									        <li>初始密码支持自定义修改策略</li>
									        <li>新增校验用户修改新密码不能与旧密码相同</li>
									        <li>添加检查密码范围支持的特殊字符包括：~!@#$%^&*()-=_+</li>
									        <li>注册账号设置默认用户名称及密码最后更新时间</li>
									        <li>去除用户手机邮箱部门必填验证</li>
									        <li>新增日期格式化方法</li>
									        <li>代码生成添加bit类型</li>
									        <li>树结构加载添加callBack回调方法</li>
									        <li>修复用户管理页面滚动返回顶部条失效</li>
									        <li>修复代码生成模板文件上传组件缺少ctx的问题</li>
									        <li>限制系统内置参数不允许删除</li>
									        <li>新增表格列宽拖动插件</li>
									        <li>新增Ajax局部刷新demo</li>
									        <li>新增是否开启页脚功能</li>
									        <li>新增表格参数（通过自定义函数设置标题样式headerStyle）</li>
									        <li>新增表格参数（通过自定义函数设置页脚样式footerStyle）</li>
									        <li>修复窗体大小改变后浮动提示框失效问题</li>
									        <li>生成代码补充必填样式</li>
									        <li>生成页面时不忽略remark属性</li>
									        <li>字典数据列表页添加关闭按钮</li>
									        <li>Excel注解支持自动统计数据总和</li>
									        <li>升级springboot到2.1.17 提升安全性</li>
									        <li>升级pagehelper到最新版1.3.0</li>
									        <li>升级druid到最新版本v1.2.1</li>
									        <li>升级fastjson到最新版1.2.74</li>
									        <li>升级bootstrap-fileinput到最新版本5.1.2</li>
									        <li>升级oshi到最新版本v5.2.5</li>
									        <li>表单向导插件更换为jquery-smartwizard</li>
									        <li>修改主子表提交示例代码防止渲染失效</li>
									        <li>添加导入数据弹出窗体自定义宽高</li>
									        <li>用户信息参数返回忽略掉密码字段</li>
									        <li>优化关闭窗体添加index参数</li>
									        <li>回显数据字典（字符串数组）增加空值判断</li>
									        <li>修改前端密码长度校验和错误提示不符问题</li>
									        <li>AjaxResult重写put方法，以方便链式调用</li>
									        <li>增强验证码校验的语义，更易懂</li>
									        <li>导入excel整形值校验优化</li>
									        <li>Excel导出类型NUMERIC支持精度浮点类型</li>
									        <li>导出Excel调整targetAttr获取值方法，防止get方法不规范</li>
									        <li>输入框组验证错误后置图标提示颜色</li>
									        <li>上传媒体类型添加视频格式</li>
									        <li>数据权限判断参数类型</li>
									        <li>修正数据库字符串类型nvarchar</li>
									        <li>优化递归子节点</li>
									        <li>修复多表格搜索formId无效</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v44">v4.4.0</a><code class="pull-right">2020.08.24</code>
								   </h5>
								</div>
								<div id="v44" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>升级bootstrapTable到最新版本1.17.1</li>
									        <li>升级shiro到最新版1.6.0 阻止权限绕过漏洞</li>
									        <li>升级fastjson到最新版1.2.73</li>
									        <li>代码生成支持同步数据库</li>
									        <li>代码生成支持富文本控件</li>
									        <li>用户密码支持自定义配置规则</li>
									        <li>新增表格自动刷新插件</li>
									        <li>新增表格打印配置插件</li>
									        <li>更换图片裁剪工具为cropper</li>
									        <li>Excel支持sort导出排序</li>
									        <li>代码生成支持自定义路径</li>
									        <li>代码生成支持选择上级菜单</li>
									        <li>代码生成支持上传控件</li>
									        <li>新增表格参数（自定义加载文本的字体大小loadingFontSize）</li>
									        <li>Excel注解支持设置BigDecimal精度&舍入规则</li>
									        <li>操作日志记录排除敏感属性字段</li>
									        <li>修复不同浏览器附件下载中文名乱码的问题</li>
									        <li>用户分配角色不允许选择超级管理员角色</li>
									        <li>更换表格冻结列插件</li>
									        <li>添加右侧冻结列示例</li>
									        <li>升级表格行编辑&移动端适应插件</li>
									        <li>修复更新表格插件后无法设置实例配置问题</li>
									        <li>修复更新表格插件后导致的主子表错误</li>
									        <li>修复页面存在多表格，回调函数res数据不正确问题</li>
									        <li>强退&过期清理登录账号缓存会话</li>
									        <li>表格树标题内容支持html语义化标签</li>
									        <li>修复配置应用的访问路径首页页签重复问题</li>
									        <li>优化openTab打开时滚动到当前页签</li>
									        <li>表格请求方式method支持自定义配置</li>
									        <li>菜单页签联动优化</li>
									        <li>用户邮箱长度限制修改为50</li>
									        <li>主子表示例添加日期格式案例</li>
									        <li>修改表格行内编辑示例旧值参数</li>
									        <li>操作日志查询方式调整</li>
									        <li>唯一限制条件只返回单条数据</li>
									        <li>修改Excel设置STRING单元格类型</li>
									        <li>添加获取当前的环境配置方法</li>
									        <li>截取返回参数长度，防止超出异常</li>
									        <li>定时任务cron表达式验证</li>
									        <li>拆分表格插件，按需引入</li>
									        <li>多行文本框补齐必填错误提示背景</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v431">v4.3.1</a><code class="pull-right">2020.07.05</code>
								   </h5>
								</div>
								<div id="v431" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>国家信息安全漏洞（请务必保持cipherKey密钥唯一性）</li>
									        <li>升级shiro到最新版1.5.3 阻止权限绕过漏洞</li>
									        <li>修改验证码在使用后清除，防止多次使用</li>
									        <li>检查字符支持小数点&降级改成异常提醒</li>
									        <li>openOptions函数中加入自定义maxmin属性</li>
									        <li>支持openOptions方法最大化</li>
									        <li>支持openOptions方法多个按钮回调</li>
									        <li>新增isLinkage支持页签与菜单联动</li>
									        <li>修改代码生成导入表结构出现异常页面不提醒问题</li>
									        <li>优化用户头像发生错误，则显示一个默认头像</li>
									        <li>Excel导出支持字典类型</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v43">v4.3.0</a><code class="pull-right">2020.06.22</code>
								   </h5>
								</div>
								<div id="v43" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>代码生成模板支持主子表</li>
									        <li>代码生成显示类型支持复选框</li>
									        <li>前端表单样式修改成圆角</li>
									        <li>新增回显数据字典（字符串数组）</li>
									        <li>修复浏览器手动缩放比例后菜单无法自适应问题</li>
									        <li>限制用户不允许选择系统管理员角色</li>
									        <li>用户信息添加输入框组图标&鼠标按下显示密码</li>
									        <li>升级fastjson到最新版1.2.70 修复高危安全漏洞</li>
									        <li>升级Bootstrap版本到v3.3.7</li>
									        <li>修复selectColumns方法获取子对象数据无效问题</li>
									        <li>修改数据源类型优先级，先根据方法，再根据类</li>
									        <li>修改上级部门（选择项排除本身和下级）</li>
									        <li>首页菜单显示调整</li>
									        <li>添加是否开启swagger配置</li>
									        <li>新增示例（主子表提交）</li>
									        <li>新增示例（多级联动下拉示例）</li>
									        <li>新增示例（表格属性data数据加载）</li>
									        <li>新增表格列参数（是否列选项可见ignore）</li>
									        <li>新增表格参数（是否启用显示卡片视图cardView）</li>
									        <li>新增表格参数（是否显示全屏按钮showFullscreen）</li>
									        <li>新增表格参数（是否启用分页条无限循环的功能paginationLoop）</li>
									        <li>新增表格参数（是否显示表头showHeader）</li>
									        <li>表格添加显示/隐藏所有列方法 showAllColumns/hideAllColumns</li>
									        <li>修复部分情况节点不展开问题</li>
									        <li>修复关闭标签页后刷新还是上次地址问题</li>
									        <li>修复选择菜单后刷新页面，菜单箭头显示不对问题</li>
											<li>修复jquery表单序列化时复选框未选中不会序列化到对象中问题</li>
											<li>Excel支持readConverterExp读取字符串组内容</li>
									        <li>更换IP地址查询接口</li>
									        <li>默认关闭获取ip地址</li>
									        <li>操作处理ajaxSuccess判断修正</li>
									        <li>HttpUtils.sendPost()方法，参数无需拼接参数到url</li>
									        <li>通用http发送方法增加参数 contentType 编码类型</li>
									        <li>HTML过滤器不替换&实体</li>
									        <li>代码生成浮点型改用BigDecimal</li>
									        <li>修复表单构建单选和多选框渲染问题</li>
									        <li>代码生成模板调整，字段为String并且必填则加空串条件</li>
									        <li>字典数据查询列表根据dictSort升序排序</li>
									        <li>修复树表对imageView和tooltip方法无效问题</li>
									        <li>修复Long类型比较相等问题调整</li>
									        <li>示例demo页面清除html链接，防止点击后跳转出现404</li>
									        <li>在线用户强退方法合并</li>
									        <li>添加校验部门包含未停用的子部门</li>
									        <li>取消回车自动提交表单</li>
									        <li>'A','I','BUTTON' 标签忽略clickToSelect事件，防止点击操作按钮时选中</li>
									        <li>邮箱显示截取部分字符串，防止低分辨率错位</li>
									        <li>代码生成列属性根据sort排序</li>
									        <li>修复更多操作部分浏览器不兼容情况</li>
									        <li>图片预览事件属性修正</li>
									        <li>修复冻结列排序样式无效问题</li>
									        <li>修复context-path的情况下个人中心刷新导致样式问题</li>
									        <li>全屏editFull打开适配表树</li>
									        <li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v42">v4.2.0</a><code class="pull-right">2020.03.23</code>
								   </h5>
								</div>
								<div id="v42" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>用户管理添加分配角色页面</li>
									        <li>定时任务添加调度日志按钮</li>
									        <li>新增是否开启用户注册功能</li>
											<li>新增页面滚动显示返回顶部按钮</li>
											<li>用户&角色&任务添加更多操作按钮</li>
											<li>iframe框架页会话过期弹出超时提示</li>
											<li>移动端登录不显示左侧菜单</li>
											<li>侧边栏添加一套深蓝色主题</li>
											<li>首页logo固定，不随菜单滚动</li>
											<li>支持mode配置history（表示去掉地址栏的#）</li>
											<li>任务分组字典翻译（调度日志详细）</li>
											<li>字典管理添加缓存读取</li>
											<li>字典数据列表标签显示样式</li>
											<li>参数管理支持缓存操作</li>
											<li>日期控件清空结束时间设置开始默认值为2099-12-31</li>
											<li>表格树添加获取数据后响应回调处理</li>
											<li>批量替换表前缀调整</li>
											<li>支持表格导入模板的弹窗表单加入其它输入控件</li>
											<li>表单重置刷新表格树</li>
											<li>新增支持导出数据字段排序</li>
											<li>新增表格参数（是否单选checkbox）</li>
											<li>druid未授权不允许访问</li>
											<li>表格树父节点兼容0,'0','',null</li>
											<li>表单必填的项添加星号</li>
											<li>修复select2不显示校验错误信息</li>
											<li>添加自定义HTML过滤器</li>
											<li>修复多数据源下开关关闭出现异常问题</li>
											<li>修复翻页记住选择项数据问题</li>
											<li>用户邮箱长度限制20</li>
											<li>修改错误页面返回主页出现嵌套问题</li>
											<li>表格浮动提示单双引号转义</li>
											<li>支持配置四级菜单</li>
											<li>升级shiro到最新版1.4.2 阻止rememberMe漏洞攻击</li>
											<li>升级summernote到最新版本v0.8.12</li>
											<li>导入Excel根据dateFormat属性格式处理</li>
											<li>修复War部署无法正常shutdown,ehcache内存泄漏</li>
											<li>修复代码生成短字段无法识别问题</li>
											<li>修复serviceImpl模版，修改方法判断日期错误</li>
											<li>代码生成模板增加导出功能日志记录</li>
											<li>代码生成唯一编号调整为tableId</li>
											<li>代码生成查询时忽略大小写</li>
											<li>代码生成支持翻页记住选中</li>
											<li>代码生成表注释未填写也允许导入</li>
											<li>Global全局配置类修改为注解，防止多环境配置下读取问题</li>
											<li>修复多表格情况下，firstLoad只对第一个表格生效</li>
											<li>处理Maven打包出现警告问题</li>
											<li>默认主题样式，防止网速慢情况下出现空白</li>
											<li>修复文件上传多级目录识别问题</li>
											<li>锚链接解码url，防止中文导致页面不能加载问题</li>
											<li>修复右键Tab页刷新事件重复请求问题</li>
											<li>角色禁用&菜单隐藏不查询权限</li>
											<li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v41">v4.1.0</a><code class="pull-right">2019.10.22</code>
								   </h5>
								</div>
								<div id="v41" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>支持多表格实例操作</li>
									        <li>浮动提示方法tooltip支持弹窗</li>
											<li>代码生成&字典数据支持模糊条件查询</li>
											<li>增加页签全屏方法</li>
											<li>增加清除表单验证错误信息方法</li>
											<li>支持iframe局部刷新页面</li>
											<li>支持在线切换主题</li>
											<li>修改图片预览设置的高宽参数颠倒问题</li>
											<li>操作日志新增解锁账户功能</li>
											<li>管理员用户&角色不允许操作</li>
											<li>去掉jsoup包调用自定义转义工具</li>
											<li>添加时间轴示例</li>
											<li>修复翻页记住选择时获取指定列值的问题</li>
											<li>代码生成sql脚本添加导出按钮</li>
											<li>添加表格父子视图示例</li>
											<li>添加表格行内编辑示例</li>
											<li>升级fastjson到最新版1.2.60 阻止漏洞攻击</li>
											<li>升级echarts到最新版4.2.1</li>
											<li>操作日志新增返回参数</li>
											<li>支持mybatis通配符扫描任意多个包</li>
											<li>权限验证多种情况处理</li>
											<li>修复树形类型的代码生成的部分必要属性无法显示</li>
											<li>修复非表格插件情况下重置出现异常</li>
											<li>修复富文本编辑器有序列表冲突</li>
											<li>代码生成表前缀配置支持多个</li>
											<li>修复自动去除表前缀配置无效问题</li>
											<li>菜单列表按钮数据可见不显示（权限标识控制）</li>
											<li>修复设置会话超时时间无效问题</li>
											<li>新增本地资源通用下载方法</li>
											<li>操作日志记录新增请求方式</li>
											<li>代码生成单选按钮属性重名修复</li>
											<li>优化select2下拉框宽度不会随浏览器改变</li>
											<li>修复代码生成树表异常</li>
											<li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v40">v4.0.0</a><code class="pull-right">2019.08.08</code>
								   </h5>
								</div>
								<div id="v40" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>代码生成支持预览、编辑，保存方案</li>
									        <li>新增防止表单重复提交注解</li>
											<li>新增后端校验（和前端保持一致）</li>
											<li>新增同一个用户最大会话数控制</li>
											<li>Excel导出子对象支持多个字段</li>
											<li>定时任务支持静态调用和多参数</li>
											<li>定时任务增加分组条件查询</li>
											<li>字典类型增加任务分组数据</li>
											<li>新增表格是否首次加载数据</li>
											<li>新增parentTab选项卡可在同一页签打开</li>
											<li>多数据源支持类注解（允许继承父类的注解）</li>
											<li>部门及以下数据权限（调整为以下及所有子节点）</li>
											<li>新增角色数据权限配（仅本人数据权限）</li>
											<li>修改菜单权限显示问题</li>
											<li>上传文件修改路径及返回名称</li>
											<li>添加报表插件及示例</li>
											<li>添加首页统计模板</li>
											<li>添加表格拖拽示例</li>
											<li>添加卡片列表示例</li>
											<li>添加富文本编辑器示例</li>
											<li>添加表格动态增删改查示例</li>
											<li>添加用户页面岗位选择框提示</li>
											<li>点击菜单操作添加背景高亮显示</li>
											<li>表格树新增showSearch是否显示检索信息</li>
											<li>解决表格列设置sortName无效问题</li>
											<li>表格图片预览支持自定义设置宽高</li>
											<li>添加表格列浮动提示（单击文本复制）</li>
											<li>PC端收起菜单后支持浮动显示</li>
											<li>详细操作样式调整</li>
											<li>修改用户更新描述空串不更新问题</li>
											<li>导入修改为模板渲染</li>
											<li>修改菜单及部门排序规则</li>
											<li>角色导出数据范围表达式翻译</li>
											<li>添加summernote富文本字体大小</li>
											<li>优化表格底部下边框防重叠&汇总像素问题</li>
											<li>树表格支持属性多层级访问</li>
											<li>修复IE浏览器用户管理界面右侧留白问题</li>
											<li>重置按钮刷新表格</li>
											<li>重置密码更新用户缓存</li>
											<li>优化验证码属性参数</li>
											<li>支持数据监控配置用户名和密码</li>
											<li>文件上传修改按钮背景及加载动画</li>
											<li>支持配置一级菜单href跳转</li>
											<li>侧边栏添加一套浅色主题</li>
											<li>树表格添加回调函数（校验异常状态）</li>
											<li>用户个人中心适配手机端显示</li>
											<li>Excel支持设置导出类型&更换样式</li>
											<li>检查属性改变修改为克隆方式（防止热部署强转异常）</li>
											<li>其他细节优化</li>
										</ol>
									</div>
							    </div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v34">v3.4.0</a><code class="pull-right">2019.06.03</code>
								   </h5>
								</div>
								<div id="v34" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
									        <li>新增实例演示菜单及demo</li>
											<li>新增页签右键操作</li>
											<li>菜单管理新增打开方式</li>
											<li>新增点击某行触发的事件</li>
											<li>新增双击某行触发的事件</li>
											<li>新增单击某格触发的事件</li>
											<li>新增双击某格触发的事件</li>
											<li>新增是否启用显示细节视图</li>
											<li>支持上传任意格式文件</li>
											<li>修复角色权限注解失效问题</li>
											<li>左侧的菜单栏宽度调整</li>
											<li>新增响应完成后自定义回调函数</li>
											<li>支持前端及其他模块直接获取用户信息</li>
											<li>升级swagger到最新版2.9.2</li>
											<li>升级jquery.slimscroll到最新版1.3.8</li>
											<li>升级select2到最新版4.0.7</li>
											<li>新增角色配置本部门数据权限</li>
											<li>新增角色配置本部门及以下数据权限</li>
											<li>优化底部操作防止跳到页面顶端</li>
											<li>修改冻结列选框无效及样式问题</li>
											<li>修复部门四层级修改祖级无效问题</li>
											<li>更换开关切换按钮样式</li>
											<li>新增select2-bootstrap美化下拉框</li>
											<li>添加表格内图片预览方法</li>
											<li>修复权限校验失败跳转页面路径错误</li>
											<li>国际化资源文件调整</li>
											<li>通知公告布局调整</li>
											<li>删除页签操作功能</li>
											<li>表格树新增查询指定列值</li>
											<li>更改系统接口扫描方式及完善测试案例</li>
											<li>表格列浮动提示及字典回显默认去背景</li>
											<li>修复启用翻页记住前面的选择check没选中问题</li>
											<li>去除监控页面底部的广告</li>
											<li>日期控件功问题修复及data功能增强</li>
											<li>新增角色权限可见性（前端直接调用）</li>
											<li>新增获取当前登录用户方法（前端及子模块调用）</li>
											<li>修复热部署重启导致菜单丢失问题</li>
											<li>优化业务校验失败普通请求跳转页面</li>
											<li>操作日志新增状态条件查询</li>
											<li>操作类型支持多选条件查询</li>
											<li>通知公告防止滚动触底回弹优化</li>
											<li>其他细节优化</li>
										</ol>
									</div>
								</div>
							 </div>
                             <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v33">v3.3.0</a><code class="pull-right">2019.04.01</code>
								   </h5>
								</div>
								<div id="v33" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
											<li>新增线程池统一管理</li>
											<li>新增支持左右冻结列</li>
											<li>新增表格字符超长浮动提示</li>
											<li>升级datepicker拓展并汉化</li>
											<li>升级druid到最新版本v1.1.14</li>
											<li>修复个人头像为图片服务器跨域问题</li>
											<li>修改上传文件按日期存储</li>
											<li>新增表格客户端分页选项</li>
											<li>新增表格的高度参数</li>
											<li>新增表格销毁方法</li>
											<li>新增表格下拉按钮切换方法</li>
											<li>新增表格分页跳转到指定页码</li>
											<li>新增表格启用点击选中行参数</li>
											<li>修复表格数据重新加载未触发部分按钮禁用</li>
											<li>使用jsonview展示操作日志参数</li>
											<li>新增方法（addTab、editTab）</li>
											<li>修改用户管理界面为Tab打开方式</li>
											<li>表单验证代码优化</li>
											<li>修复@Excel注解 prompt 属性使用报错</li>
											<li>修复combo属性Excel兼容性问题</li>
											<li>新增@Excel导入导出支持父类字段</li>
											<li>修复关闭最后选项卡无法激活滚动问题</li>
											<li>增加日期控件显示类型及回显格式扩展选项</li>
											<li>修复定时任务执行失败后入库状态为成功状态</li>
											<li>支持定时任务并发开关控制</li>
											<li>优化权限校验失败普通请求跳转页面</li>
											<li>捕获线程池执行任务抛出的异常</li>
											<li>修复IE浏览器导出功能报错</li>
											<li>新增角色管理分配用户功能</li>
											<li>新增表格翻页记住前面的选择</li>
											<li>调整用户个人中心页面</li>
											<li>修复界面存在的一些安全问题</li>
											<li>其他细节优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v32">v3.2.0</a><code class="pull-right">2019.01.18</code>
								   </h5>
								</div>
								<div id="v32" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
											<li>部门修改时不允许选择最后节点</li>
											<li>修复部门菜单排序字段无效</li>
											<li>修复光驱磁盘导致服务监控异常</li>
											<li>登录界面去除check插件</li>
											<li>验证码文本字符间距修正</li>
											<li>升级SpringBoot到最新版本2.1.1</li>
											<li>升级MYSQL驱动</li>
											<li>修正登录必填项位置偏移</li>
											<li>Session会话检查优化</li>
											<li>Excel注解支持多级获取</li>
											<li>新增序列号生成方法</li>
											<li>修复WAR部署tomcat退出线程异常</li>
											<li>全屏操作增加默认确认/关闭</li>
											<li>修复个人信息可能导致漏洞</li>
											<li>字典数据根据下拉选择新增类型</li>
											<li>升级Summernote到最新版本v0.8.11</li>
											<li>新增用户数据导入</li>
											<li>首页主题样式更换</li>
											<li>layer扩展主题更换</li>
											<li>用户管理移动端默认隐藏左侧布局</li>
											<li>详细信息弹出层显示在顶层</li>
											<li>表格支持切换状态（用户/角色/定时任务）</li>
											<li>Druid数据源支持配置继承</li>
											<li>修正部分iPhone手机端表格适配问题</li>
											<li>新增防止重复提交表单方法</li>
											<li>新增表格数据统计汇总方法</li>
											<li>支持富文本上传图片文件</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v31">v3.1.0</a><code class="pull-right">2018.12.03</code>
								   </h5>
								</div>
								<div id="v31" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
											<li>新增内网不获取IP地址</li>
											<li>新增cron表达式有效校验</li>
											<li>定时任务新增详细信息</li>
											<li>定时任务默认策略修改（不触发立即执行）</li>
											<li>定时任务显示下一个执行周期</li>
											<li>支持前端任意日期格式处理</li>
											<li>上传头像删除多余提交按钮</li>
											<li>表格增加行间隔色配置项</li>
											<li>表格增加转义HTML字符串配置项</li>
											<li>表格增加显示/隐藏指定列</li>
											<li>代码生成优化</li>
											<li>操作日志参数格式化显示</li>
											<li>页签新增新增全屏显示</li>
											<li>新增一键打包部署</li>
											<li>Excel注解新增多个参数</li>
											<li>新增提交静默更新表格方法</li>
											<li>新增服务监控菜单</li>
										</ol>
									</div>
								</div>
							</div>
							<div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v30">v3.0.0</a><code class="pull-right">2018.10.08</code>
								   </h5>
								</div>
								<div id="v30" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
											<li>升级poi到最新版3.17</li>
											<li>导出修改临时目录绝对路径</li>
											<li>升级laydate到最新版5.0.9</li>
											<li>升级SpringBoot到最新版本2.0.5</li>
											<li>优化开始/结束时间校验限制</li>
											<li>重置密码参数表中获取默认值</li>
											<li>修复头像修改显示问题</li>
											<li>新增数据权限过滤注解</li>
											<li>新增表格检索折叠按钮</li>
											<li>新增清空（登录、操作、调度）日志</li>
											<li>固定按钮位置（提交/关闭）</li>
											<li>部门/菜单支持（展开/折叠）</li>
											<li>部分细节调整优化</li>
											<li>项目采用分模块</li>
										</ol>
									</div>
								</div>
							</div>
							<div class="panel panel-default">
								<div class="panel-heading">
								   <h5 class="panel-title">
									   <a data-toggle="collapse" data-parent="#version" href="#v24">v2.4.0</a><code class="pull-right">2018.09.03</code>
								   </h5>
								</div>
								<div id="v24" class="panel-collapse collapse">
									<div class="panel-body">
									   <ol>
											<li>支持部门多级查询</li>
											<li>修复菜单状态查询无效</li>
											<li>支持IP地址开关</li>
											<li>支持XSS开关</li>
											<li>记录日志异步处理</li>
											<li>字典回显样式更改为下拉框</li>
											<li>菜单类型必填校验</li>
											<li>修复在线用户排序报错</li>
											<li>增加重置按钮</li>
											<li>支持注解导入数据</li>
											<li>支持弹层外区域关闭</li>
											<li>备注更换为文本区域</li>
											<li>新增角色逻辑删除</li>
											<li>新增部门逻辑删除</li>
											<li>支持部门数据权限</li>
											<li>管理员默认拥有所有授权</li>
											<li>字典数据采用分页</li>
											<li>部分细节调整优化</li>
										</ol>
									</div>
								</div>
							</div>
                            <div class="panel panel-default">
									<div class="panel-heading">
									   <h5 class="panel-title">
										   <a data-toggle="collapse" data-parent="#version" href="#v23">v2.3.0</a><code class="pull-right">2018.08.06</code>
									   </h5>
									</div>
									<div id="v23" class="panel-collapse collapse">
										<div class="panel-body">
										   <ol>
										        <li>支持表格不分页开关控制</li>
										        <li>修改字典类型同步修改字典数据</li>
										        <li>代码生成新增修改后缀处理</li>
										        <li>代码生成新增实体toString</li>
										        <li>代码生成非字符串去除!=''</li>
												<li>导出数据前加载遮罩层</li>
												<li>部门删除校验条件修改</li>
												<li>搜索查询下载优化</li>
												<li>手机打开弹出层自适应</li>
												<li>角色岗位禁用显示置灰</li>
												<li>角色禁用不显示菜单</li>
												<li>新增导出权限</li>
												<li>角色权限唯一校验</li>
												<li>岗位名称编码唯一校验</li>
                                                <li>TreeTable优化</li>
                                                <li>支持多数据源</li>
												<li>其他细节优化</li>
											</ol>
										</div>
									</div>
								</div>
                                <div class="panel panel-default">
									<div class="panel-heading">
									   <h5 class="panel-title">
										   <a data-toggle="collapse" data-parent="#version" href="#v22">v2.2.0</a><code class="pull-right">2018.07.23</code>
									   </h5>
									</div>
									<div id="v22" class="panel-collapse collapse">
										<div class="panel-body">
										   <ol>
										        <li>修复批量生成代码异常问题</li>
										        <li>修复定时器保存失败问题</li>
										        <li>修复热部署转换问题</li>
												<li>支持查询菜单管理，部门管理</li>
												<li>大多数功能支持时间查询</li>
												<li>自定义导出注解自动匹配column</li>
												<li>新增任务执行策略</li>
												<li>操作详细动态显示类型</li>
												<li>支持动态回显字典数据</li>
												<li>后台代码优化调整</li>
												<li>其他细节优化</li>
											</ol>
										</div>
									</div>
								</div>
                                <div class="panel panel-default">
									<div class="panel-heading">
									   <h5 class="panel-title">
										   <a data-toggle="collapse" data-parent="#version" href="#v21">v2.1.0</a><code class="pull-right">2018.07.10</code>
									   </h5>
									</div>
									<div id="v21" class="panel-collapse collapse">
										<div class="panel-body">
										   <ol>
										        <li>新增登录超时提醒</li>
										        <li>修复定时器热部署转换问题</li>
										        <li>修复登录验证码校验无效问题</li>
												<li>定时任务新增立即执行一次</li>
												<li>存在字典数据不允许删除字典</li>
												<li>字典数据支持按名称查询</li>
												<li>代码生成增加日志注解&表格优化</li>
												<li>修复用户逻辑删除后能登录问题</li>
												<li>表格支持多字段动态排序</li>
												<li>支持三级菜单显示</li>
												<li>新增ry.sh启动程序脚本</li>
												<li>其他细节优化</li>
											</ol>
										</div>
									</div>
								</div>
                            	<div class="panel panel-default">
									<div class="panel-heading">
									   <h5 class="panel-title">
										   <a data-toggle="collapse" data-parent="#version" href="#v20">v2.0.0</a><code class="pull-right">2018.07.02</code>
									   </h5>
									</div>
									<div id="v20" class="panel-collapse collapse">
										<div class="panel-body">
										   <ol>
										        <li>升级SpringBoot到最新版本2.0.3</li>
										        <li>新增公告管理</li>
												<li>表单校验示提体验优化</li>
												<li>前端通用方法封装调整</li>
												<li>前端去除js文件，合并到html</li>
												<li>操作加载遮罩层</li>
												<li>支持全屏模式操作</li>
												<li>支持注解导出数据</li>
												<li>系统支持多查询&下载</li>
												<li>系统样式调整</li>
											</ol>
										</div>
									</div>
								</div>
                                <div class="panel panel-default">
									<div class="panel-heading">
									   <h5 class="panel-title">
										   <a data-toggle="collapse" data-parent="#version" href="#v16">v1.1.6</a><code class="pull-right">2018.06.04</code>
									   </h5>
									</div>
									<div id="v16" class="panel-collapse collapse">
										<div class="panel-body">
										   <ol>
												<li>新增用户列表部门列</li>
												<li>新增登录地点</li>
												<li>新增swagger</li>
												<li>修复排序数字校验</li>
												<li>优化头像上传文件类型限定为图片</li>
												<li>新增XSS过滤</li>
												<li>新增热部署提高开发效率</li>
												<li>修复treegrid居中无效</li>
												<li>角色多条件查询</li>
											</ol>
										</div>
									</div>
								</div>
                            	<div class="panel panel-default">
									<div class="panel-heading">
									   <h5 class="panel-title">
										   <a data-toggle="collapse" data-parent="#version" href="#v15">v1.1.5</a><code class="pull-right">2018.05.28</code>
									   </h5>
									</div>
									<div id="v15" class="panel-collapse collapse">
										<div class="panel-body">
										   <ol>
												<li>优化登录失败刷新验证码</li>
												<li>新增用户登录地址时间</li>
												<li>修复ajax超时退出问题</li>
												<li>新增html调用数据字典(若依首创)</li>
												<li>调整系统部分样式</li>
												<li>新增用户逻辑删除</li>
												<li>新增管理员不允许删除修改</li>
												<li>升级bootstrapTable到最新版本1.12.1</li>
												<li>升级layer到最新版本3.1.1</li>
											</ol>
										</div>
									</div>
								</div>
							    <div class="panel panel-default">
									<div class="panel-heading">
									   <h5 class="panel-title">
										   <a data-toggle="collapse" data-parent="#version" href="#v14">v1.1.4</a><code class="pull-right">2018.05.20</code>
									   </h5>
									</div>
									<div id="v14" class="panel-collapse collapse">
										<div class="panel-body">
										   <ol>
												<li>新增参数管理</li>
												<li>修复头像上传bug</li>
												<li>手机邮箱唯一校验</li>
												<li>支持手机邮箱登录</li>
												<li>代码生成优化</li>
												<li>支持模糊查询</li>
												<li>支持切换主题皮肤</li>
												<li>修改权限即时生效</li>
												<li>修复页签Tab关闭问题</li>
											</ol>
										</div>
									</div>
								</div>
								<div class="panel panel-default">
									<div class="panel-heading">
									   <h5 class="panel-title">
										   <a data-toggle="collapse" data-parent="#version" href="#v13">v1.1.3</a><code class="pull-right">2018.05.14</code>
									   </h5>
									</div>
									<div id="v13" class="panel-collapse collapse">
										<div class="panel-body">
										   <ol>
												<li>新增验证码（数组计算、字符验证）</li>
												<li>新增cookie记住我</li>
												<li>新增头像上传</li>
												<li>用户名密码长度限制</li>
												<li>通用字段提取</li>
												<li>支持自定义条件查询</li>
												<li>部门名称必填、时间格式调整</li>
												<li>其他细节优化</li>
											</ol>
										</div>
									</div>
								</div>
								<div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v12">v1.1.2</a><code class="pull-right">2018.05.07</code>
										</h5>
                                    </div>
                                    <div id="v12" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增个人信息修改</li>
												<li>菜单存在子菜单不允许删除</li>
												<li>菜单分配角色不允许删除</li>
												<li>角色分配人员不允许删除</li>
												<li>岗位使用后不允许删除</li>
												<li>保证用户的数据完整性加入事物</li>
												<li>新增环境使用手册、数据建模</li>
												<li>Thymeleaf升级到3.0</li>
												<li>支持非ROOT部署</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v11">v1.1.1</a><code class="pull-right">2018.04.23</code>
										</h5>
                                    </div>
                                    <div id="v11" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增表单构建器</li>
												<li>代码生成优化</li>
												<li>支持新增主部门</li>
												<li>支持选择上级部门、上级菜单</li>
												<li>新增字典管理单条删除</li>
												<li>优化一些其他细节</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v10">v1.1.0</a><code class="pull-right">2018.04.20</code>
										</h5>
                                    </div>
                                    <div id="v10" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>支持密码盐</li>
												<li>支持新增主目录</li>
												<li>支持批量生成代码</li>
												<li>支持表格导出(csv、txt、doc、excel)</li>
												<li>自动适应宽高模式窗体</li>
												<li>重复校验(角色名、菜单名、部门名)</li>
												<li>优化一些其他细节</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v09">v1.0.9</a><code class="pull-right">2018.04.14</code>
										</h5>
                                    </div>
                                    <div id="v09" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增代码生成(生成包括 java、html、js、xml、sql)</li>
												<li>新增按钮权限控制隐藏(若依首创)</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
								<div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v08">v1.0.8</a><code class="pull-right">2018.04.08</code>
										</h5>
                                    </div>
                                    <div id="v08" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增定时任务(新增、修改、删除、查询、启动/暂停)</li>
												<li>新增调度日志(查询、删除)</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            	<div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v07">v1.0.7</a><code class="pull-right">2018.04.04</code>
										</h5>
                                    </div>
                                    <div id="v07" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增岗位管理(新增、修改、删除、查询)</li>
												<li>优化用户管理，菜单管理部分细节</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
								<div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v06">v1.0.6</a><code class="pull-right">2018.03.15</code>
										</h5>
                                    </div>
                                    <div id="v06" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增字典管理(新增、删除、修改、查询、数据选择)</li>
												<li>新增用户密码重置</li>
												<li>优化一些其他细节</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
								<div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v05">v1.0.5</a><code class="pull-right">2018.03.12</code>
										</h5>
                                    </div>
                                    <div id="v05" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增菜单管理(新增、删除、修改、查询、图标选择)</li>
												<li>部门管理优化(添加责任人、联系电话、邮箱、修改者)</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
								<div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v04">v1.0.4</a><code class="pull-right">2018.03.11</code>
										</h5>
                                    </div>
                                    <div id="v04" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增角色管理(新增、删除、修改、查询、菜单选择)</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
								<div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v03">v1.0.3</a><code class="pull-right">2018.03.08</code>
										</h5>
                                    </div>
                                    <div id="v03" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增用户管理(新增、删除、修改、查询、部门选择)</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            	<div class="panel panel-default">
									<div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v02">v1.0.2</a><code class="pull-right">2018.03.04</code>
										</h5>
                                    </div>
                                    <div id="v02" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增部门管理 (新增、删除、修改、查询)</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h5 class="panel-title">
											<a data-toggle="collapse" data-parent="#version" href="#v01">v1.0.1</a><code class="pull-right">2018.03.03</code>
										</h5>
                                    </div>
                                    <div id="v01" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                            	<li>新增在线用户 (批量强退、单条强退、查询)</li>
                                                <li>新增登录日志 (批量删除、查询)</li>
												<li>新增操作日志 (批量删除、查询、详细)</li>
												<li>新增数据监控 (监控DB池连接和SQL的执行)</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" data-parent="#version" href="#v00">v1.0.0</a><code class="pull-right">2018.03.01</code>
                                        </h4>
                                    </div>
                                    <div id="v00" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <ol>
                                                <li>若依管理系统正式发布。</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>捐赠</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-warning">
                            	请作者喝杯咖啡（点击图片放大）
                        </div>
                        <p id="pay-qrcode">
                            <a href="javascript:;"><img th:src="@{/img/pay.png}" width="100%" alt="请使用手机支付宝或者微信扫码支付">
                            </a>
                        </p>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script type="text/javascript">
	    // 显示测试项目详情
	    function showTestItems() {
	        parent.layer.open({
	            title: '🎯 国家学生体质健康标准测试项目',
	            type: 1,
	            area: ['600px', '500px'],
	            content: `
	                <div style="padding: 20px;">
	                    <h4>必测项目：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><strong>身高体重：</strong>测量学生身高、体重，计算BMI指数</li>
	                        <li><strong>肺活量：</strong>测试学生呼吸系统功能</li>
	                        <li><strong>50米跑：</strong>测试学生速度素质和神经系统灵活性</li>
	                        <li><strong>坐位体前屈：</strong>测试学生柔韧性</li>
	                    </ul>
	                    <h4>选测项目：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><strong>立定跳远：</strong>测试学生下肢爆发力</li>
	                        <li><strong>仰卧起坐（女）：</strong>测试女学生腹肌耐力</li>
	                        <li><strong>引体向上（男）：</strong>测试男学生上肢力量</li>
	                        <li><strong>800米跑（女）/1000米跑（男）：</strong>测试学生心肺耐力</li>
	                    </ul>
	                    <p style="color: #666; margin-top: 15px;">
	                        <i class="fa fa-info-circle"></i>
	                        所有测试项目均按照教育部《国家学生体质健康标准》执行
	                    </p>
	                </div>
	            `
	        });
	    }

	    // 显示系统功能
	    function showSystemFeatures() {
	        parent.layer.open({
	            title: '🔧 系统核心功能',
	            type: 1,
	            area: ['600px', '500px'],
	            content: `
	                <div style="padding: 20px;">
	                    <h4>数据管理：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><i class="fa fa-users"></i> 学生信息批量导入与管理</li>
	                        <li><i class="fa fa-calendar"></i> 测试时间安排与预约管理</li>
	                        <li><i class="fa fa-edit"></i> 体测数据录入与修改</li>
	                        <li><i class="fa fa-shield"></i> 数据安全与权限控制</li>
	                    </ul>
	                    <h4>统计分析：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><i class="fa fa-bar-chart"></i> 个人成绩统计与等级评定</li>
	                        <li><i class="fa fa-line-chart"></i> 班级、院系、全校数据对比</li>
	                        <li><i class="fa fa-pie-chart"></i> 体质健康趋势分析</li>
	                        <li><i class="fa fa-file-pdf-o"></i> 多维度报告自动生成</li>
	                    </ul>
	                    <h4>系统特色：</h4>
	                    <ul style="line-height: 1.8;">
	                        <li><i class="fa fa-mobile"></i> 支持PC端和移动端操作</li>
	                        <li><i class="fa fa-cloud"></i> 数据云端存储，安全可靠</li>
	                        <li><i class="fa fa-cogs"></i> 灵活的权限配置和角色管理</li>
	                    </ul>
	                </div>
	            `
	        });
	    }
    </script>
</body>
</html>
