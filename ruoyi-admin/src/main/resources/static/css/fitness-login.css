/* 大学生体质测试管理系统 - 登录页面增强样式 */

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 页面加载动画 */
.signin-info {
    animation: fadeInLeft 0.8s ease-out;
}

.login-form {
    animation: fadeInRight 0.8s ease-out 0.2s both;
}

.signup-footer {
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* 输入框聚焦效果增强 */
.form-control {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-control:focus {
    transform: translateY(-2px);
}

/* 按钮点击效果 */
.btn-login:active {
    transform: translateY(0) scale(0.98);
}

/* 特性列表动画 */
.feature-list li {
    animation: fadeInLeft 0.6s ease-out;
}

.feature-list li:nth-child(1) { animation-delay: 0.1s; }
.feature-list li:nth-child(2) { animation-delay: 0.2s; }
.feature-list li:nth-child(3) { animation-delay: 0.3s; }
.feature-list li:nth-child(4) { animation-delay: 0.4s; }
.feature-list li:nth-child(5) { animation-delay: 0.5s; }
.feature-list li:nth-child(6) { animation-delay: 0.6s; }

/* 验证码图片悬停效果 */
.captcha-image {
    border: 2px solid #e8ecef;
    transition: all 0.3s ease;
}

.captcha-image:hover {
    border-color: #4facfe;
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.2);
}

/* 错误提示样式美化 */
label.error {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
    display: block;
    animation: fadeInUp 0.3s ease;
}

/* 记住我复选框美化 */
.checkbox-custom input[type="checkbox"] {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-custom input[type="checkbox"]:checked {
    background: #4facfe;
    border-color: #4facfe;
}

.checkbox-custom input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 响应式优化 */
@media (max-width: 992px) {
    .signin-info {
        text-align: center;
    }
    
    .feature-list {
        display: inline-block;
        text-align: left;
    }
}

@media (max-width: 576px) {
    .signinpanel {
        margin: 10px;
    }
    
    .signin-info, .login-form {
        padding: 30px 20px;
    }
    
    .captcha-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .captcha-input {
        width: 100%;
    }
}

/* 加载状态样式 */
.btn-login[disabled] {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-login .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成功/错误消息样式 */
.alert-custom {
    border-radius: 8px;
    border: none;
    padding: 12px 16px;
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

.alert-success {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
}

.alert-error {
    background: linear-gradient(135deg, #e17055, #d63031);
    color: white;
}

/* 链接悬停效果 */
a {
    position: relative;
    transition: all 0.3s ease;
}

a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background: currentColor;
    transition: width 0.3s ease;
}

a:hover::after {
    width: 100%;
}

/* 图标动画 */
.fa {
    transition: all 0.3s ease;
}

.feature-list li:hover .fa {
    transform: scale(1.2);
    color: #00f2fe;
}

/* 背景装饰元素 */
.signin-info::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
}

/* 表单验证状态 */
.form-control.is-valid {
    border-color: #00b894;
    box-shadow: 0 0 0 3px rgba(0, 184, 148, 0.1);
}

.form-control.is-invalid {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .signinpanel {
        background: rgba(30, 30, 30, 0.95);
    }
    
    .login-form {
        background: #2c2c2c;
        color: #ffffff;
    }
    
    .form-control {
        background: #3c3c3c;
        border-color: #555;
        color: #ffffff;
    }
    
    .login-title {
        color: #ffffff;
    }
    
    .login-subtitle {
        color: #cccccc;
    }
}
