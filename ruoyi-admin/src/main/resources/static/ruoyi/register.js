
$(function() {
    validateRule();
    $('.imgcode').click(function() {
        var url = ctx + "captcha/captchaImage?type=" + captchaType + "&s=" + Math.random();
        $(".imgcode").attr("src", url);
    });
});

function register() {
    var username = $.common.trim($("input[name='username']").val());
    var password = $.common.trim($("input[name='password']").val());
    var email = $.common.trim($("input[name='email']").val());
    var phone = $.common.trim($("input[name='phone']").val());
    var validateCode = $("input[name='validateCode']").val();

    if($.common.isEmpty(validateCode) && captchaEnabled) {
        $.modal.msg("请输入验证码");
        return false;
    }

    $.ajax({
        type: "post",
        url: ctx + "register",
        data: {
            "loginName": username,
            "password": password,
            "email": email,
            "phonenumber": phone,
            "validateCode": validateCode
        },
        beforeSend: function () {
            $.modal.loading($("#btnSubmit").data("loading"));
        },
        success: function(r) {
            if (r.code == web_status.SUCCESS) {
            	layer.alert("🎉 恭喜您，账号 <strong>" + username + "</strong> 注册成功！<br/><br/>📧 我们已向您的邮箱 " + email + " 发送了确认信息<br/>📱 重要通知将发送到您的手机 " + phone + "<br/>⏳ 请等待管理员审核激活您的账号", {
            	    icon: 1,
            	    title: "注册成功",
            	    area: ['450px', '280px']
            	},
            	function(index) {
            	    //关闭弹窗
            	    layer.close(index);
            	    location.href = ctx + 'login';
            	});
            } else {
            	$.modal.closeLoading();
            	$('.imgcode').click();
            	$(".code").val("");
            	$.modal.msg(r.msg);
            }
        }
    });
}

function validateRule() {
    var icon = "<i class='fa fa-times-circle'></i> ";
    $("#registerForm").validate({
        rules: {
            username: {
                required: true,
                minlength: 2,
                maxlength: 20
            },
            email: {
                required: true,
                email: true,
                maxlength: 50
            },
            phone: {
                required: true,
                minlength: 11,
                maxlength: 11,
                digits: true
            },
            password: {
                required: true,
                minlength: 6,
                maxlength: 20
            },
            confirmPassword: {
                required: true,
                equalTo: "[name='password']"
            },
            acceptTerm: {
                required: true
            }
        },
        messages: {
            username: {
                required: icon + "请输入学号或工号",
                minlength: icon + "用户名不能少于2个字符",
                maxlength: icon + "用户名不能超过20个字符"
            },
            email: {
                required: icon + "请输入邮箱地址",
                email: icon + "请输入正确的邮箱格式",
                maxlength: icon + "邮箱地址不能超过50个字符"
            },
            phone: {
                required: icon + "请输入手机号码",
                minlength: icon + "请输入11位手机号码",
                maxlength: icon + "请输入11位手机号码",
                digits: icon + "手机号码只能包含数字"
            },
            password: {
            	required: icon + "请设置登录密码",
                minlength: icon + "密码不能少于6个字符",
                maxlength: icon + "密码不能超过20个字符"
            },
            confirmPassword: {
                required: icon + "请再次输入密码",
                equalTo: icon + "两次密码输入不一致"
            },
            acceptTerm: {
                required: icon + "请阅读并同意用户协议"
            }
        },
        submitHandler: function(form) {
            register();
        }
    })
}
