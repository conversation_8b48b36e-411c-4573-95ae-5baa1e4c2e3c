package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.system.service.ISysReservationService;
import com.ruoyi.common.core.domain.entity.SysReservation;

/**
 * 体测预约管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/reservation")
public class SysReservationController extends BaseController
{
    private String prefix = "system/reservation";

    @Autowired
    private ISysReservationService reservationService;

    /**
     * 体测预约管理主页面
     */
    @RequiresPermissions("system:reservation:view")
    @GetMapping()
    public String reservation()
    {
        return prefix + "/reservation";
    }

    /**
     * 查询体测预约列表
     */
    @RequiresPermissions("system:reservation:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysReservation reservation)
    {
        startPage();
        List<SysReservation> list = reservationService.selectSysReservationList(reservation);
        return getDataTable(list);
    }

    /**
     * 导出体测预约设置
     */
    @RequiresPermissions("system:reservation:export")
    @Log(title = "体测预约", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysReservation reservation)
    {
        List<SysReservation> list = reservationService.selectSysReservationList(reservation);
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        return util.exportExcel(list, "体测预约设置");
    }

    /**
     * 新增体测预约设置页面
     */
    @RequiresPermissions("system:reservation:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存体测预约设置
     */
    @RequiresPermissions("system:reservation:add")
    @Log(title = "体测预约", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysReservation reservation)
    {
        reservation.setCreateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.insertSysReservation(reservation));
    }

    /**
     * 修改体测预约设置页面
     */
    @RequiresPermissions("system:reservation:edit")
    @GetMapping("/edit/{reservationId}")
    public String edit(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation reservation = reservationService.selectSysReservationByReservationId(reservationId);
        mmap.put("reservation", reservation);
        return prefix + "/edit";
    }

    /**
     * 修改保存体测预约设置
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "体测预约", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysReservation reservation)
    {
        reservation.setUpdateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.updateSysReservation(reservation));
    }

    /**
     * 删除体测预约设置
     */
    @RequiresPermissions("system:reservation:remove")
    @Log(title = "体测预约", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(reservationService.deleteSysReservationByReservationIds(ids));
    }

    /**
     * 开放预约
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "开放预约", businessType = BusinessType.UPDATE)
    @PostMapping("/open/{reservationId}")
    @ResponseBody
    public AjaxResult openReservation(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation reservation = reservationService.selectSysReservationByReservationId(reservationId);
        if (reservation == null)
        {
            return AjaxResult.error("预约信息不存在");
        }
        
        reservation.setIsOpen("1");
        reservation.setUpdateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.updateSysReservation(reservation));
    }

    /**
     * 关闭预约
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "关闭预约", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{reservationId}")
    @ResponseBody
    public AjaxResult closeReservation(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation reservation = reservationService.selectSysReservationByReservationId(reservationId);
        if (reservation == null)
        {
            return AjaxResult.error("预约信息不存在");
        }
        
        reservation.setIsOpen("0");
        reservation.setUpdateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.updateSysReservation(reservation));
    }

    /**
     * 克隆预约设置
     */
    @RequiresPermissions("system:reservation:add")
    @Log(title = "克隆预约", businessType = BusinessType.INSERT)
    @PostMapping("/clone/{reservationId}")
    @ResponseBody
    public AjaxResult cloneReservation(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation original = reservationService.selectSysReservationByReservationId(reservationId);
        if (original == null)
        {
            return AjaxResult.error("原预约信息不存在");
        }
        
        // 创建克隆对象
        SysReservation clone = new SysReservation();
        clone.setReservationCode(original.getReservationCode() + "_COPY");
        clone.setReservationName(original.getReservationName() + "_副本");
        clone.setVenueId(original.getVenueId());
        clone.setVenueName(original.getVenueName());
        clone.setTestDate(original.getTestDate());
        clone.setStartTime(original.getStartTime());
        clone.setEndTime(original.getEndTime());
        clone.setMaleQuota(original.getMaleQuota());
        clone.setFemaleQuota(original.getFemaleQuota());
        clone.setTotalQuota(original.getTotalQuota());
        clone.setReservationType(original.getReservationType());
        clone.setTestItems(original.getTestItems());
        clone.setRequirements(original.getRequirements());
        clone.setContactPerson(original.getContactPerson());
        clone.setContactPhone(original.getContactPhone());
        clone.setIsOpen("0"); // 默认关闭
        clone.setOrderNum(original.getOrderNum());
        clone.setStatus("0");
        clone.setCreateBy(ShiroUtils.getLoginName());
        
        return toAjax(reservationService.insertSysReservation(clone));
    }

    /**
     * 选择场地树
     */
    @GetMapping("/selectVenueTree")
    public String selectVenueTree()
    {
        return prefix + "/venueTree";
    }

    /**
     * 导入模板
     */
    @RequiresPermissions("system:reservation:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        return util.importTemplateExcel("体测预约数据");
    }

    /**
     * 导入数据
     */
    @RequiresPermissions("system:reservation:import")
    @Log(title = "体测预约", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        List<SysReservation> reservationList = util.importExcel(file.getInputStream());
        String operName = ShiroUtils.getLoginName();
        String message = reservationService.importReservation(reservationList, updateSupport, operName);
        return AjaxResult.success(message);
    }
}
