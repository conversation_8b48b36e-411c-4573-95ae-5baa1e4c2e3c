package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Date;
import java.text.SimpleDateFormat;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.system.service.ISysReservationService;
import com.ruoyi.common.core.domain.entity.SysReservation;

/**
 * 体测预约管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/reservation")
public class SysReservationController extends BaseController
{
    private String prefix = "system/reservation";

    @Autowired
    private ISysReservationService reservationService;

    /**
     * 时间格式绑定
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // 日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(false);
        binder.registerCustomEditor(Date.class, "testDate", new CustomDateEditor(dateFormat, true));

        // 时间格式
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
        timeFormat.setLenient(false);
        binder.registerCustomEditor(Date.class, "startTime", new CustomDateEditor(timeFormat, true));
        binder.registerCustomEditor(Date.class, "endTime", new CustomDateEditor(timeFormat, true));
    }

    /**
     * 体测预约管理主页面
     */
    @RequiresPermissions("system:reservation:view")
    @GetMapping()
    public String reservation()
    {
        return prefix + "/reservation";
    }

    /**
     * 查询体测预约列表
     */
    @RequiresPermissions("system:reservation:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysReservation reservation)
    {
        startPage();
        List<SysReservation> list = reservationService.selectSysReservationList(reservation);
        return getDataTable(list);
    }

    /**
     * 导出体测预约设置
     */
    @RequiresPermissions("system:reservation:export")
    @Log(title = "体测预约", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysReservation reservation)
    {
        List<SysReservation> list = reservationService.selectSysReservationList(reservation);
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        return util.exportExcel(list, "体测预约设置");
    }

    /**
     * 新增体测预约设置页面
     */
    @RequiresPermissions("system:reservation:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存体测预约设置
     */
    @RequiresPermissions("system:reservation:add")
    @Log(title = "体测预约", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysReservation reservation)
    {
        // 调试信息
        System.out.println("接收到的预约信息:");
        System.out.println("预约名称: " + reservation.getReservationName());
        System.out.println("开始时间: " + reservation.getStartTime());
        System.out.println("结束时间: " + reservation.getEndTime());

        // 验证必填字段
        if (reservation.getStartTime() == null)
        {
            return AjaxResult.error("开始时间不能为空，请选择正确的时间格式(HH:mm:ss)");
        }
        if (reservation.getEndTime() == null)
        {
            return AjaxResult.error("结束时间不能为空，请选择正确的时间格式(HH:mm:ss)");
        }

        reservation.setCreateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.insertSysReservation(reservation));
    }

    /**
     * 修改体测预约设置页面
     */
    @RequiresPermissions("system:reservation:edit")
    @GetMapping("/edit/{reservationId}")
    public String edit(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation reservation = reservationService.selectSysReservationByReservationId(reservationId);
        mmap.put("reservation", reservation);
        return prefix + "/edit";
    }

    /**
     * 修改保存体测预约设置
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "体测预约", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysReservation reservation)
    {
        reservation.setUpdateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.updateSysReservation(reservation));
    }

    /**
     * 删除体测预约设置
     */
    @RequiresPermissions("system:reservation:remove")
    @Log(title = "体测预约", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(reservationService.deleteSysReservationByReservationIds(ids));
    }

    /**
     * 开放预约
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "开放预约", businessType = BusinessType.UPDATE)
    @PostMapping("/open/{reservationId}")
    @ResponseBody
    public AjaxResult openReservation(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation reservation = reservationService.selectSysReservationByReservationId(reservationId);
        if (reservation == null)
        {
            return AjaxResult.error("预约信息不存在");
        }
        
        reservation.setIsOpen("1");
        reservation.setUpdateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.updateSysReservation(reservation));
    }

    /**
     * 关闭预约
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "关闭预约", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{reservationId}")
    @ResponseBody
    public AjaxResult closeReservation(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation reservation = reservationService.selectSysReservationByReservationId(reservationId);
        if (reservation == null)
        {
            return AjaxResult.error("预约信息不存在");
        }
        
        reservation.setIsOpen("0");
        reservation.setUpdateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.updateSysReservation(reservation));
    }

    /**
     * 克隆预约设置
     */
    @RequiresPermissions("system:reservation:add")
    @Log(title = "克隆预约", businessType = BusinessType.INSERT)
    @PostMapping("/clone/{reservationId}")
    @ResponseBody
    public AjaxResult cloneReservation(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation original = reservationService.selectSysReservationByReservationId(reservationId);
        if (original == null)
        {
            return AjaxResult.error("原预约信息不存在");
        }
        
        // 创建克隆对象
        SysReservation clone = new SysReservation();
        clone.setReservationCode(original.getReservationCode() + "_COPY");
        clone.setReservationName(original.getReservationName() + "_副本");
        clone.setVenueId(original.getVenueId());
        clone.setVenueName(original.getVenueName());
        clone.setTestDate(original.getTestDate());
        clone.setStartTime(original.getStartTime());
        clone.setEndTime(original.getEndTime());
        clone.setMaleQuota(original.getMaleQuota());
        clone.setFemaleQuota(original.getFemaleQuota());
        clone.setTotalQuota(original.getTotalQuota());
        clone.setReservationType(original.getReservationType());
        clone.setTestItems(original.getTestItems());
        clone.setRequirements(original.getRequirements());
        clone.setContactPerson(original.getContactPerson());
        clone.setContactPhone(original.getContactPhone());
        clone.setIsOpen("0"); // 默认关闭
        clone.setOrderNum(original.getOrderNum());
        clone.setStatus("0");
        clone.setCreateBy(ShiroUtils.getLoginName());
        
        return toAjax(reservationService.insertSysReservation(clone));
    }

    /**
     * 学生预约页面
     */
    @RequiresPermissions("system:reservation:list")
    @GetMapping("/studentReservation/{reservationId}")
    public String studentReservation(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation reservation = reservationService.selectSysReservationByReservationId(reservationId);
        if (reservation == null || !"1".equals(reservation.getIsOpen()))
        {
            mmap.put("error", "预约不存在或未开放");
            return prefix + "/error";
        }

        mmap.put("reservation", reservation);
        return prefix + "/studentReservation";
    }

    /**
     * 预约详情页面
     */
    @RequiresPermissions("system:reservation:list")
    @GetMapping("/detail/{reservationId}")
    public String detail(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation reservation = reservationService.selectSysReservationByReservationId(reservationId);
        if (reservation == null)
        {
            mmap.put("error", "预约不存在");
            return prefix + "/error";
        }

        mmap.put("reservation", reservation);
        return prefix + "/detail";
    }



    /**
     * 选择场地树
     */
    @GetMapping("/selectVenueTree")
    public String selectVenueTree()
    {
        return prefix + "/venueTree";
    }

    /**
     * 导入模板
     */
    @RequiresPermissions("system:reservation:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        return util.importTemplateExcel("体测预约数据");
    }

    /**
     * 导入数据
     */
    @RequiresPermissions("system:reservation:import")
    @Log(title = "体测预约", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        List<SysReservation> reservationList = util.importExcel(file.getInputStream());
        String operName = ShiroUtils.getLoginName();

        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int failureNum = 0;

        for (SysReservation reservation : reservationList)
        {
            try
            {
                reservation.setCreateBy(operName);
                reservationService.insertSysReservation(reservation);
                successNum++;
                successMsg.append("<br/>" + successNum + "、预约 " + reservation.getReservationName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、预约 " + reservation.getReservationName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }

        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            return AjaxResult.error(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return AjaxResult.success(successMsg.toString());
        }
    }
}
