package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysReservation;
import com.ruoyi.common.core.domain.entity.SysReservationRecord;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysReservationRecordService;
import com.ruoyi.system.service.ISysReservationService;

import static com.ruoyi.common.utils.LogUtils.getUsername;

/**
 * 预约管理 操作处理
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/reservation")
public class SysReservationController extends BaseController
{
    private String prefix = "system/reservation";

    @Autowired
    private ISysReservationService reservationService;

    @Autowired
    private ISysReservationRecordService recordService;

    @RequiresPermissions("system:reservation:view")
    @GetMapping()
    public String reservation()
    {
        return prefix + "/reservation";
    }

    /**
     * 查询预约管理列表
     */
    @RequiresPermissions("system:reservation:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysReservation reservation)
    {
        startPage();
        List<SysReservation> list = reservationService.selectReservationList(reservation);
        return getDataTable(list);
    }

    /**
     * 导出预约管理列表
     */
    @RequiresPermissions("system:reservation:export")
    @Log(title = "预约管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public void export(HttpServletResponse response, SysReservation reservation)
    {
        List<SysReservation> list = reservationService.selectReservationList(reservation);
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        util.exportExcel(response, list, "预约管理数据");
    }

    /**
     * 新增预约管理
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存预约管理
     */
    @RequiresPermissions("system:reservation:add")
    @Log(title = "预约管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated SysReservation reservation)
    {
        if (!reservationService.checkReservationCodeUnique(reservation))
        {
            return error("新增预约失败，预约编码已存在");
        }
        reservation.setCreateBy(ShiroUtils.getLoginName());
        return toAjax(reservationService.insertReservation(reservation));
    }

    /**
     * 修改预约管理
     */
    @RequiresPermissions("system:reservation:edit")
    @GetMapping("/edit/{reservationId}")
    public String edit(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation reservation = reservationService.selectReservationById(reservationId);
        mmap.put("reservation", reservation);
        return prefix + "/edit";
    }

    /**
     * 修改保存预约管理
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "预约管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated SysReservation reservation)
    {
        if (!reservationService.checkReservationCodeUnique(reservation))
        {
            return error("修改预约失败，预约编码已存在");
        }
        reservation.setUpdateBy(getUsername());
        return toAjax(reservationService.updateReservation(reservation));
    }

    /**
     * 删除预约管理
     */
    @RequiresPermissions("system:reservation:remove")
    @Log(title = "预约管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(reservationService.deleteReservationByIds(ids));
    }

    /**
     * 校验预约编码
     */
    @PostMapping("/checkReservationCodeUnique")
    @ResponseBody
    public boolean checkReservationCodeUnique(SysReservation reservation)
    {
        return reservationService.checkReservationCodeUnique(reservation);
    }

    /**
     * 开放预约
     */
    @RequiresPermissions("system:reservation:open")
    @Log(title = "预约管理", businessType = BusinessType.UPDATE)
    @PostMapping("/open")
    @ResponseBody
    public AjaxResult openReservation(Long reservationId)
    {
        return toAjax(reservationService.openReservation(reservationId));
    }

    /**
     * 关闭预约
     */
    @RequiresPermissions("system:reservation:close")
    @Log(title = "预约管理", businessType = BusinessType.UPDATE)
    @PostMapping("/close")
    @ResponseBody
    public AjaxResult closeReservation(Long reservationId)
    {
        return toAjax(reservationService.closeReservation(reservationId));
    }

    /**
     * 克隆预约
     */
    @RequiresPermissions("system:reservation:clone")
    @Log(title = "预约管理", businessType = BusinessType.INSERT)
    @PostMapping("/clone")
    @ResponseBody
    public AjaxResult cloneReservation(Long reservationId)
    {
        return toAjax(reservationService.cloneReservation(reservationId));
    }

    /**
     * 预约记录页面
     */
    @RequiresPermissions("system:reservation:record")
    @GetMapping("/record/{reservationId}")
    public String record(@PathVariable("reservationId") Long reservationId, ModelMap mmap)
    {
        SysReservation reservation = reservationService.selectReservationById(reservationId);
        mmap.put("reservation", reservation);
        return prefix + "/record";
    }

    /**
     * 查询预约记录列表
     */
    @RequiresPermissions("system:reservation:record")
    @PostMapping("/record/list")
    @ResponseBody
    public TableDataInfo recordList(SysReservationRecord record)
    {
        startPage();
        List<SysReservationRecord> list = recordService.selectRecordList(record);
        return getDataTable(list);
    }

    /**
     * 导出预约记录列表
     */
    @RequiresPermissions("system:reservation:export")
    @Log(title = "预约记录", businessType = BusinessType.EXPORT)
    @PostMapping("/record/export")
    @ResponseBody
    public void exportRecord(HttpServletResponse response, SysReservationRecord record)
    {
        List<SysReservationRecord> list = recordService.selectRecordList(record);
        ExcelUtil<SysReservationRecord> util = new ExcelUtil<SysReservationRecord>(SysReservationRecord.class);
        util.exportExcel(response, list, "预约记录数据");
    }

    /**
     * 取消预约
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "预约记录", businessType = BusinessType.UPDATE)
    @PostMapping("/record/cancel")
    @ResponseBody
    public AjaxResult cancelRecord(Long recordId, String cancelReason)
    {
        return toAjax(recordService.cancelReservation(recordId, cancelReason));
    }

    /**
     * 签到
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "预约记录", businessType = BusinessType.UPDATE)
    @PostMapping("/record/checkin")
    @ResponseBody
    public AjaxResult checkIn(Long recordId)
    {
        return toAjax(recordService.checkIn(recordId));
    }

    /**
     * 导入预约数据
     */
    @RequiresPermissions("system:reservation:add")
    @Log(title = "预约管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        List<SysReservation> reservationList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = reservationService.importReservation(reservationList, updateSupport, operName);
        return success(message);
    }

    /**
     * 下载模板
     */
    @RequiresPermissions("system:reservation:add")
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SysReservation> util = new ExcelUtil<SysReservation>(SysReservation.class);
        util.importTemplateExcel(response, "预约数据");
    }

    /**
     * 选择场地树
     */
    @GetMapping("/selectVenueTree")
    public String selectVenueTree()
    {
        return prefix + "/venueTree";
    }

    /**
     * 加载场地列表树
     */
    @GetMapping("/venueTreeData")
    @ResponseBody
    public List<SysReservation> venueTreeData()
    {
        List<SysReservation> reservationList = reservationService.selectReservationAll();
        return reservationList;
    }
}
