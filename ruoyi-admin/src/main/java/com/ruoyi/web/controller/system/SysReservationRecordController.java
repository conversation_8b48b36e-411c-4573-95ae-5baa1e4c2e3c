package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysReservation;
import com.ruoyi.common.core.domain.entity.SysReservationRecord;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysReservationRecordService;
import com.ruoyi.system.service.ISysReservationService;

/**
 * 预约记录 操作处理
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/record")
public class SysReservationRecordController extends BaseController
{
    private String prefix = "system/record";

    @Autowired
    private ISysReservationRecordService recordService;

    @Autowired
    private ISysReservationService reservationService;

    @RequiresPermissions("system:reservation:record")
    @GetMapping()
    public String record()
    {
        return prefix + "/record";
    }

    /**
     * 查询预约记录列表
     */
    @RequiresPermissions("system:reservation:record")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysReservationRecord record)
    {
        startPage();
        List<SysReservationRecord> list = recordService.selectRecordList(record);
        return getDataTable(list);
    }

    /**
     * 导出预约记录列表
     */
    @RequiresPermissions("system:reservation:export")
    @Log(title = "预约记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public void export(HttpServletResponse response, SysReservationRecord record)
    {
        List<SysReservationRecord> list = recordService.selectRecordList(record);
        ExcelUtil<SysReservationRecord> util = new ExcelUtil<SysReservationRecord>(SysReservationRecord.class);
        util.exportExcel(response, list, "预约记录数据");
    }

    /**
     * 新增预约记录
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存预约记录
     */
    @RequiresPermissions("system:reservation:add")
    @Log(title = "预约记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated SysReservationRecord record)
    {
        record.setCreateBy(ShiroUtils.getLoginName());
        return toAjax(recordService.insertRecord(record));
    }

    /**
     * 修改预约记录
     */
    @RequiresPermissions("system:reservation:edit")
    @GetMapping("/edit/{recordId}")
    public String edit(@PathVariable("recordId") Long recordId, ModelMap mmap)
    {
        SysReservationRecord record = recordService.selectRecordById(recordId);
        mmap.put("record", record);
        return prefix + "/edit";
    }

    /**
     * 修改保存预约记录
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "预约记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated SysReservationRecord record)
    {
        record.setUpdateBy(ShiroUtils.getLoginName());
        return toAjax(recordService.updateRecord(record));
    }

    /**
     * 删除预约记录
     */
    @RequiresPermissions("system:reservation:remove")
    @Log(title = "预约记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(recordService.deleteRecordByIds(ids));
    }

    /**
     * 用户预约
     */
    @PostMapping("/makeReservation")
    @ResponseBody
    public AjaxResult makeReservation(Long reservationId)
    {
        Long userId = ShiroUtils.getUserId();
        return toAjax(recordService.makeReservation(reservationId, userId));
    }

    /**
     * 取消预约
     */
    @PostMapping("/cancel")
    @ResponseBody
    public AjaxResult cancelReservation(Long recordId, String cancelReason)
    {
        return toAjax(recordService.cancelReservation(recordId, cancelReason));
    }

    /**
     * 签到
     */
    @RequiresPermissions("system:reservation:edit")
    @Log(title = "预约记录", businessType = BusinessType.UPDATE)
    @PostMapping("/checkin")
    @ResponseBody
    public AjaxResult checkIn(Long recordId)
    {
        return toAjax(recordService.checkIn(recordId));
    }

    /**
     * 我的预约记录
     */
    @GetMapping("/my")
    public String myRecord()
    {
        return prefix + "/myRecord";
    }

    /**
     * 查询我的预约记录列表
     */
    @PostMapping("/my/list")
    @ResponseBody
    public TableDataInfo myRecordList(SysReservationRecord record)
    {
        startPage();
        Long userId = ShiroUtils.getUserId();
        record.setUserId(userId);
        List<SysReservationRecord> list = recordService.selectRecordList(record);
        return getDataTable(list);
    }

    /**
     * 预约详情
     */
    @GetMapping("/detail/{recordId}")
    public String detail(@PathVariable("recordId") Long recordId, ModelMap mmap)
    {
        SysReservationRecord record = recordService.selectRecordById(recordId);
        if (record != null)
        {
            SysReservation reservation = reservationService.selectReservationById(record.getReservationId());
            mmap.put("reservation", reservation);
        }
        mmap.put("record", record);
        return prefix + "/detail";
    }

    /**
     * 检查用户预约状态
     */
    @PostMapping("/checkUserReservation")
    @ResponseBody
    public AjaxResult checkUserReservation(Long reservationId)
    {
        Long userId = ShiroUtils.getUserId();
        SysReservationRecord record = recordService.checkUserReservation(reservationId, userId);
        if (record != null)
        {
            return AjaxResult.success("已预约", record);
        }
        else
        {
            return AjaxResult.error("未预约");
        }
    }

    /**
     * 获取预约统计信息
     */
    @GetMapping("/stats/{reservationId}")
    @ResponseBody
    public AjaxResult getReservationStats(@PathVariable("reservationId") Long reservationId)
    {
        SysReservation reservation = reservationService.selectReservationById(reservationId);
        if (reservation != null)
        {
            // 更新统计数据
            reservationService.updateReservationStats(reservationId);
            // 重新查询最新数据
            reservation = reservationService.selectReservationById(reservationId);
            return AjaxResult.success(reservation);
        }
        return AjaxResult.error("预约不存在");
    }
}
