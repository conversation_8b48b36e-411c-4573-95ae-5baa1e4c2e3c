package com.ruoyi.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysVenue;
import com.ruoyi.system.service.ISysVenueService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 场地管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Controller
@RequestMapping("/system/venue")
public class SysVenueController extends BaseController
{
    private String prefix = "system/venue";

    @Autowired
    private ISysVenueService sysVenueService;

    @RequiresPermissions("system:venue:view")
    @GetMapping()
    public String venue()
    {
        return prefix + "/venue";
    }

    /**
     * 查询场地管理列表
     */
    @RequiresPermissions("system:venue:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysVenue sysVenue)
    {
        startPage();
        List<SysVenue> list = sysVenueService.selectSysVenueList(sysVenue);
        return getDataTable(list);
    }

    /**
     * 导出场地管理列表
     */
    @RequiresPermissions("system:venue:export")
    @Log(title = "场地管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysVenue sysVenue)
    {
        List<SysVenue> list = sysVenueService.selectSysVenueList(sysVenue);
        ExcelUtil<SysVenue> util = new ExcelUtil<SysVenue>(SysVenue.class);
        return util.exportExcel(list, "场地管理数据");
    }

    /**
     * 新增场地管理
     */
    @RequiresPermissions("system:venue:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存场地管理
     */
    @RequiresPermissions("system:venue:add")
    @Log(title = "场地管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysVenue sysVenue)
    {
        return toAjax(sysVenueService.insertSysVenue(sysVenue));
    }

    /**
     * 获取场地详情
     */
    @RequiresPermissions("system:venue:list")
    @GetMapping("/{venueId}")
    @ResponseBody
    public AjaxResult getInfo(@PathVariable("venueId") Long venueId)
    {
        SysVenue sysVenue = sysVenueService.selectSysVenueByVenueId(venueId);
        if (sysVenue != null)
        {
            return AjaxResult.success(sysVenue);
        }
        return AjaxResult.error("场地不存在");
    }

    /**
     * 修改场地管理
     */
    @RequiresPermissions("system:venue:edit")
    @GetMapping("/edit/{venueId}")
    public String edit(@PathVariable("venueId") Long venueId, ModelMap mmap)
    {
        SysVenue sysVenue = sysVenueService.selectSysVenueByVenueId(venueId);
        mmap.put("sysVenue", sysVenue);
        return prefix + "/edit";
    }

    /**
     * 修改保存场地管理
     */
    @RequiresPermissions("system:venue:edit")
    @Log(title = "场地管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysVenue sysVenue)
    {
        return toAjax(sysVenueService.updateSysVenue(sysVenue));
    }

    /**
     * 删除场地管理
     */
    @RequiresPermissions("system:venue:remove")
    @Log(title = "场地管理", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(sysVenueService.deleteSysVenueByVenueIds(ids));
    }
}
